config:store:delete --all web/unsecure/base_url
config:store:delete --all web/unsecure/base_link_url
config:store:delete --all web/secure/base_url
config:store:delete --all web/secure/base_link_url
config:store:delete --all web/unsecure/base_static_url
config:store:delete --all web/secure/base_static_url
config:store:delete --all web/unsecure/base_media_url
config:store:delete --all web/secure/base_media_url
config:store:delete --all web/cookie/cookie_domain
config:store:delete --all wordpress/setup/db_host
config:store:delete --all wordpress/setup/db_user
config:store:delete --all wordpress/setup/db_password
config:store:delete --all wordpress/setup/wp_home
config:store:delete --all catalog/search/elasticsearch_server_hostname
config:store:delete --all catalog/search/elasticsearch5_server_hostname
config:store:delete --all catalog/search/elasticsearch6_server_hostname
config:store:delete --all catalog/search/elasticsearch7_server_hostname
config:store:delete --all smtp/module/*
config:store:delete --all smtp/general/*
config:store:delete --all smtp/configuration_option/*
config:store:delete --all smtp/developer/*

config:store:set --scope=default --scope-id=0 web/unsecure/base_url https://www.keh-m2.loc/
config:store:set --scope=default --scope-id=0 web/unsecure/base_link_url https://www.keh-m2.loc/
config:store:set --scope=default --scope-id=0 web/secure/base_url https://www.keh-m2.loc/
config:store:set --scope=default --scope-id=0 web/secure/base_link_url https://www.keh-m2.loc/
config:store:set --scope=default --scope-id=0 web/unsecure/base_static_url https://www.keh-m2.loc/static/
config:store:set --scope=default --scope-id=0 web/secure/base_static_url https://www.keh-m2.loc/static/
config:store:set --scope=default --scope-id=0 web/unsecure/base_media_url https://www.keh-m2.loc/media/
config:store:set --scope=default --scope-id=0 web/secure/base_media_url https://www.keh-m2.loc/media/
config:store:set --scope=default --scope-id=0 web/cookie/cookie_domain www.keh-m2.loc
config:store:set --scope=default --scope-id=0 wordpress/setup/db_host db
config:store:set --scope=default --scope-id=0 wordpress/setup/db_user inchoo
config:store:set --scope=default --scope-id=0 --encrypt wordpress/setup/db_password inchoo
config:store:set --scope=default --scope-id=0 wordpress/setup/wp_home https://www.keh-m2.loc/blog/
config:set --scope=default --scope-code=0 admin/security/session_lifetime 86400
config:store:set --scope=default --scope-id=0 catalog/search/elasticsearch_server_hostname elasticsearch
config:store:set --scope=default --scope-id=0 catalog/search/elasticsearch5_server_hostname elasticsearch
config:store:set --scope=default --scope-id=0 catalog/search/elasticsearch6_server_hostname elasticsearch
config:store:set --scope=default --scope-id=0 catalog/search/elasticsearch7_server_hostname elasticsearch
config:set --scope=default --scope-code=0 admin/security/session_lifetime 86400
