#!/bin/bash
set -x

export NAMESPACE=magento-production

setSkipPostDeployFlag() {
    POD_NAME=$(kubectl get pods --namespace $NAMESPACE | grep keh-m2-backend | head -n1 | cut -d" " -f1)
    kubectl exec --namespace $NAMESPACE $POD_NAME -- bash -c 'cd var; [ -f no.skip-post-deploy ] && mv no.skip-post-deploy .skip-post-deploy || >.skip-post-deploy '
}

setExecutePostDeployFlag() {
    POD_NAME=$(kubectl get pods --namespace $NAMESPACE | grep keh-m2-backend | head -n1 | cut -d" " -f1)
    # which means no skip
    kubectl exec --namespace $NAMESPACE $POD_NAME -- bash -c 'cd var; [ -f .skip-post-deploy ] && mv .skip-post-deploy no.skip-post-deploy || >no.skip-post-deploy'
}

setPostDeployFlag() {
    gcloud auth activate-service-account <EMAIL> --key-file=/home/<USER>/gcloud_secret_keh_production.json --project=keh-production
    gcloud container clusters get-credentials production --location=us-central1

    if [[ "$1" == "EXECUTE" ]]
    then
        setExecutePostDeployFlag
    else
        setSkipPostDeployFlag
    fi
}
