#!/bin/bash
set -x

export APISERVER=https://kubernetes.default.svc
export SERVICEACCOUNT=/var/run/secrets/kubernetes.io/serviceaccount
export NAMESPACE=$(cat ${SERVICEACCOUNT}/namespace)
export TOKEN=$(cat ${SERVICEACCOUNT}/token)
export CACERT=${SERVICEACCOUNT}/ca.crt
# install jq
apt-get update
apt-get install jq
# call k8s rest api
# wait for PODS
PODS=$(curl --cacert ${CACERT} --header "Authorization: Bearer ${TOKEN}" -X GET ${APISERVER}/api/v1/namespaces/magento-staging/pods?labelSelector=app%3Dkeh-m2-backend | jq .items[].spec.containers[].image)
echo "#${PODS}#"
while [[ "${PODS}" != *"${TAG}"* ]]
do
    echo "waiting for POD"
    sleep 10
    PODS=$(curl --cacert ${CACERT} --header "Authorization: Bearer ${TOKEN}" -X GET ${APISERVER}/api/v1/namespaces/magento-staging/pods?labelSelector=app%3Dkeh-m2-backend | jq .items[].spec.containers[].image)
done
echo "PODS with images ${TAG} found"
# wait for post-deploy finish
export EVENT_AFTER_DATE=$(date -d "5 minutes ago" +'%FT%TZ')
EVENTS=$(curl --cacert ${CACERT} --header "Authorization: Bearer ${TOKEN}" -X GET ${APISERVER}/api/v1/namespaces/magento-staging/events | jq .items[] | jq "select (.message == \"Job completed\")" | jq "select (.involvedObject.name == \"keh-m2-post-deploy\")" | jq "select (.metadata.creationTimestamp > \"$EVENT_AFTER_DATE\")")
echo "#${EVENTS}#"
while [[ "${EVENTS}" == "" ]]
do
    echo "waiting for post-deployment to finish"
    sleep 10
    EVENTS=$(curl --cacert ${CACERT} --header "Authorization: Bearer ${TOKEN}" -X GET ${APISERVER}/api/v1/namespaces/magento-staging/events | jq .items[] | jq "select (.message == \"Job completed\")" | jq "select (.involvedObject.name == \"keh-m2-post-deploy\")" | jq "select (.metadata.creationTimestamp > \"$EVENT_AFTER_DATE\")")
done
echo "JOB for post deployment completed"
