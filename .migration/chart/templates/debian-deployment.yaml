---
apiVersion: apps/v1
kind: Deployment
metadata:
  namespace: {{ .Release.Namespace }}
  name: magento-media-migrator
  labels:
    app: magento-media-migrator
spec:
  replicas: 1
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 1800
  selector:
    matchLabels:
      app: magento-media-migrator
  template:
    metadata:
      annotations:
        rollme: {{ randAlphaNum 5 }} # Automatically Roll Deployments
      labels:
        app: magento-media-migrator
        short_sha: "{{ .Values.tag }}"
    spec:
      volumes:
        - name: {{ .Values.mediaNfs }}
          persistentVolumeClaim:
            claimName: {{ .Values.mediaClaimName }}
      containers:
        - image: "{{ .Values.image }}:{{ .Values.tag}}"
          imagePullPolicy: {{ .Values.imagePullPolicy }}
          name: magento-media-migrator
          args:
            - /bin/sh
            - -c
            - touch /tmp/healthy; sleep 600000
{{/*            - touch /tmp/healthy; sleep 30; rm -f /tmp/healthy; sleep 600000*/}}
          volumeMounts:
            - name: {{ .Values.mediaNfs }}
              mountPath: {{ .Values.mediaMountPath }}
          livenessProbe:
            exec:
              command:
                - cat
                - /tmp/healthy
            periodSeconds: 15
            successThreshold: 1
            failureThreshold: 5
            timeoutSeconds: 5
            initialDelaySeconds: 5
          readinessProbe:
            exec:
              command:
                - cat
                - /tmp/healthy
            periodSeconds: 15
            successThreshold: 1
            failureThreshold: 5
            timeoutSeconds: 5
            initialDelaySeconds: 5
          {{- with .Values.resources }}
          resources:
            {{- toYaml . | nindent 12 }}
          {{- end }}
          securityContext:
            capabilities:
              drop:
                - NET_RAW
            runAsUser: 0
{{- if eq (default .Values.useNodePool false) true }}
      nodeSelector:
        node_pool: {{ .Values.nodePool }}
{{- end }}
