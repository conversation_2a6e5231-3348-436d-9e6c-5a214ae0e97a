FROM gcr.io/keh-internal-development/keh-web/base_image:0367fc47696d81d5db550bf1d6e9631c5c332438

## envs
ENV INSTALL_DIR=/var/www/sites/keh.com/files/html
ENV COMPOSER_ALLOW_SUPERUSER=1

ARG ENV
ARG SELFCERT_C="US"
ARG SELFCERT_ST="GA"
ARG SELFCERT_O="KEH, Inc."
ARG SELFCERT_CN="localhost"
ARG SELFCERT_SAN="DNS:*.com"
RUN openssl req -x509 \
	-nodes \
	-days 90 \
	-subj "/C=${SELFCERT_C}/ST=${SELFCERT_ST}/O=${SELFCERT_O}/CN=${SELFCERT_CN}" \
	-addext "subjectAltName=${SELFCERT_SAN}" \
	-newkey rsa:2048 \
	-keyout /etc/ssl/private/nginx-selfsigned.key \
	-out /etc/ssl/certs/nginx-selfsigned.crt

## set working dir
WORKDIR $INSTALL_DIR

# Install composer dependencies.
COPY --chown=www-data:www-data --chmod=777 . .
COPY --chown=www-data:www-data --chmod=777 ./etc/config.php app/etc/config.php
COPY --chown=www-data:www-data --chmod=777 ./etc/env.php app/etc/env.php

RUN mkdir -p /var/www/sites/keh.com/files/html/var/page_cache /var/www/sites/keh.com/files/html/var/cache /var/www/sites/keh.com/files/html/var/log

RUN composer install --ignore-platform-reqs --prefer-dist --ansi --no-dev --no-interaction

# More performant command:
#find app/code lib vendor pub/static app/etc generated/code generated/metadata var/view_preprocessed \( -type f -or -type d \) -exec chmod u-w {} + && chmod o-rwx app/etc/env.php && chmod u+x bin/magento
ARG ENV
#COPY . .
# COPY system/applications/magento/etc/config.$ENV.php app/etc/config.php
#COPY ./etc/build.env.php app/etc/env.php
#COPY ./etc/build.env.php app/etc/env.php
COPY ./etc/config.php app/etc/config.php
COPY ./etc/env.php app/etc/build.env.php
COPY ./etc/build.env.php app/etc/env.php
RUN	find . -type d -exec chmod 755 {} \;
RUN	find ./var -type d -exec chmod 777 {} \;
RUN	find ./pub/media -type d -exec chmod 777 {} \;
RUN	find ./pub/static -type d -exec chmod 777 {} \;
RUN	chmod 777 ./app/etc
RUN	chmod 644 ./app/etc/*.xml
RUN	chmod 777 ./bin/magento
RUN php bin/magento module:enable --all
RUN	php -dmemory_limit=5G bin/magento setup:di:compile
RUN	chmod -R 777 app/etc var generated
#RUN	chmod 777 ./bin/magento
#RUN php bin/magento module:enable --all
#RUN	php -dmemory_limit=5G bin/magento setup:di:compile
#RUN composer dump-autoload --optimize
#RUN	php -dmemory_limit=5G bin/magento setup:static-content:deploy

## expose
EXPOSE 80
EXPOSE 443
EXPOSE 9000

## service configuration.
## Also prints the current code short SHA so we can ensure that the image was built with a
##   certain version of the code in the /workspace/ easily from looking at the build log
ARG SHORT_SHA
# inside keh-web
RUN echo "${SHORT_SHA}" > $INSTALL_DIR/gcpbuild.txt && \
    mkdir /run/php && \
    touch /run/php/keh.sock && \
    chown www-data:www-data /run/php/keh.sock && \
    mkdir -p /var/www/sites/keh.com/logs/php-fpm && \
    mkdir -p /var/www/sites/keh.com/logs/nginx/ && \
    mv /usr/local/etc/php-fpm.d/www.conf /usr/local/etc/php-fpm.d/www.conf.original && \
    mv /usr/local/etc/php-fpm.d/zz-docker.conf /usr/local/etc/php-fpm.d/zz-docker.conf.original

## copy service configs, more likely to change
COPY ./etc/php-fpm/. /usr/local/etc/php-fpm.d/
COPY ./etc/php/. /usr/local/etc/php/conf.d/
COPY ./etc/nginx/site.conf /etc/nginx/sites-enabled/default


## start services script
COPY --chmod=775 ./build_magento.sh /build_magento.sh
COPY --chmod=775 ./start_services.sh /start_services.sh
COPY --chmod=775 ./mount.sh /mount.sh
RUN mkdir -p /var/www/sites/keh.com/files/html/var/page_cache
RUN mkdir -p /var/www/sites/keh.com/files/html/var/cache
RUN mkdir -p /var/www/sites/keh.com/files/html/var/log
RUN mkdir -p /var/www/sites/keh.com/files/html/pub/media/catalog/product
RUN mkdir -p /var/www/sites/keh.com/files/html/pub/media/captcha

COPY ./etc/env.php app/etc/env.php

CMD /start_services.sh


# Install composer dependencies.
COPY --chown=www-data:www-data --chmod=777 . .
COPY --chown=www-data:www-data --chmod=777 ./etc/config.php app/etc/config.php
#COPY --chown=www-data:www-data --chmod=777 ./etc/env.php app/etc/env.php

RUN mkdir -p /var/www/sites/keh.com/files/html/var/page_cache /var/www/sites/keh.com/files/html/var/cache /var/www/sites/keh.com/files/html/var/log
RUN	chmod -R 777 app/etc var generated pub/media
RUN chown -R www-data:www-data app/etc var generated pub/media

RUN composer install --ignore-platform-reqs --prefer-dist --ansi --no-dev --no-suggest --no-interaction

## expose
EXPOSE 80
EXPOSE 443
EXPOSE 9000
