import html from "eslint-plugin-html";
import js from "@eslint/js";
import globals from "globals";
import { FlatCompat } from "@eslint/eslintrc";
import path from "node:path";
import { fileURLToPath } from "node:url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all,
});

export default [
    {
        ignores: ["**/node_modules", "../css/styles.css", "../js/zxcvbn.js"],
    },
    ...compat.extends("eslint:recommended"),
    {
        plugins: {
            html,
        },
        languageOptions: {
            globals: { ...globals.browser },
            ecmaVersion: 2018,
            sourceType: "module",
        },
        settings: {
            "html/html-extensions": [".html", ".phtml"],
        },
        rules: {
            indent: ["error", 2],
            "linebreak-style": ["error", "unix"],
            quotes: ["error", "single"],
            semi: ["error", "always"],
        },
        files: ["./**/*.phtml"],  // Target .phtml files
        processor: "html/html",  // Use the HTML processor to handle script tags
    },
];
