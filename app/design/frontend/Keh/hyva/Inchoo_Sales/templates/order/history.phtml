<?php
use Magento\Sales\Block\Order\History;
use Magento\Framework\Escaper;

/** @var History $block */
/** @var Escaper $escaper */

$orders = $block->getOrders();
$th_css_class = $block->getThCssClass() ?? '';
$tr_css_class = $block->getTrCssClass() ?? '';
$td_css_class = $block->getTdCssClass() ?? '';
?>
<?= $block->getChildHtml('info') ?>
<?php if ($orders && count($orders)): ?>
    <?php if ($block->getPagerHtml()): ?>
        <div class="order-products-toolbar toolbar bottom"><?= $block->getPagerHtml() ?></div>
    <?php endif ?>
    <div class="table-wrapper orders-history my-8">
        <table class="w-full" id="my-orders-table">
            <thead class="hidden md:table-header-group">
                <tr class="bg-neutral-400">
                    <th scope="col" class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> id"><?= $escaper->escapeHtml(__('Order #')) ?></th>
                    <th scope="col" class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> date"><?= $escaper->escapeHtml(__('Date')) ?></th>
                    <?= $block->getChildHtml('extra.column.header') ?>
                    <th scope="col" class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> shipping"><?= $escaper->escapeHtml(__('Ship To')) ?></th>
                    <th scope="col" class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> total"><?= $escaper->escapeHtml(__('Order Total')) ?></th>
                    <th scope="col" class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> status"><?= $escaper->escapeHtml(__('Status')) ?></th>
                    <th scope="col" class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> actions"><?= $escaper->escapeHtml(__('Action')) ?></th>
                </tr>
            </thead>
            <tbody>
            <?php foreach ($orders as $_order): ?>
                <?php $extra = $block->getChildBlock('extra.container'); ?>

                <tr class="<?= $escaper->escapeHtmlAttr($tr_css_class) ?>">
                    <td class="<?= $escaper->escapeHtmlAttr($td_css_class) ?> id" data-th="<?= $escaper->escapeHtmlAttr(__('Order #')) ?>"><?= $_order->getRealOrderId() ?></td>
                    <td class="<?= $escaper->escapeHtmlAttr($td_css_class) ?> date" data-th="<?= $escaper->escapeHtmlAttr(__('Date')) ?>"><?= $block->formatDate($_order->getCreatedAt()) ?></td>
                    <?php if ($extra): ?>
                        <?= $extra->setOrder($_order)->getChildHtml() ?>
                    <?php endif; ?>
                    <td class="<?= $escaper->escapeHtmlAttr($td_css_class) ?> shipping" data-th="<?= $escaper->escapeHtmlAttr(__('Ship To')) ?>"><?= $_order->getShippingAddress() ? $escaper->escapeHtml($_order->getShippingAddress()->getName()) : '&nbsp;' ?></td>
                    <td class="<?= $escaper->escapeHtmlAttr($td_css_class) ?> total" data-th="<?= $escaper->escapeHtmlAttr(__('Order Total')) ?>"><?= $_order->formatPrice($_order->getGrandTotal()) ?></td>
                    <td class="<?= $escaper->escapeHtmlAttr($td_css_class) ?> status" data-th="<?= $escaper->escapeHtmlAttr(__('Status')) ?>"><?= $this->helper('Inchoo\Sales\Helper\Order\Status')->getStatusHtml($_order) ?></td>
                    <td class="<?= $escaper->escapeHtmlAttr($td_css_class) ?> actions" data-th="<?= $escaper->escapeHtmlAttr(__('Actions')) ?>">
                        <a href="<?= $block->getViewUrl($_order) ?>" class="action view">
                            <span><?= $escaper->escapeHtml(__('View Order')) ?></span>
                        </a>
                    </td>
                </tr>
            <?php endforeach; ?>
            </tbody>
        </table>
    </div>
<?php else: ?>
    <div class="message info empty"><span><?= $escaper->escapeHtml(__('You have placed no orders.')) ?></span></div>
<?php endif ?>
