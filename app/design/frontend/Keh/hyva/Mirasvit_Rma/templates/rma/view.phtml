<?php
declare(strict_types=1);

use Mirasvit\Rma\Block\Rma\View;
use Hyva\Theme\ViewModel\HeroiconsOutline;

/** @var View $block */
/** @var HeroiconsOutline $heroicons */

$heroicons = $viewModels->require(HeroiconsOutline::class);


$rma      = $block->getRma();
$orders   = $block->getOrders();
$progress = $block->getProgress();

$statusMessage = $block->getStatusMessage();

$isMultiorders = count($orders) > 1;
$counter = 1;
?>
<div class="mst-rma-<?php if ($block->isCustomerLoggedIn()) : ?>view<?php else: ?>guest<?php endif ?>">
    <?php if ($progress): ?>
        <ul class="opc-progress-bar flex flex-wrap md:flex-nowrap items-start justify-center my-8 gap-4">
            <?php foreach ($progress as $key => $item): ?>
                <?php if (!$item['visible']) {
                    continue;
                } ?>
                <li class="opc-progress-bar-item <?php if ($item['active']): ?>_active<?php endif ?>
                    flex-[1_1_40%] md:flex-auto
                    relative
                    md:w-[12.5rem] lg:w-[13.89rem]
                    max-md:[&:not(:nth-child(2n))]:before:content-none
                    max-sm:[&:not(:first-child)]:before:content-['\00B7_\00B7_\00B7_\00B7']
                    sm:max-lg:[&:not(:first-child)]:before:content-['\00B7_\00B7_\00B7_\00B7_\00B7']
                    lg:[&:not(:first-child)]:before:content-['\00B7_\00B7_\00B7_\00B7_\00B7_\00B7']
                    [&:not(:first-child)]:before:text-[32px]
                    [&:not(:first-child)]:before:tracking-[0.4rem]
                    [&:not(:first-child)]:before:absolute
                    [&:not(:first-child)]:before:left-0
                    [&:not(:first-child)]:before:transform
                    [&:not(:first-child)]:before:-translate-x-1/2
                    [&:not(:first-child)]:before:-translate-y-[10%]
                    ">
                    <span class="uppercase flex flex-col items-center">
                        <span class="rounded-full w-10 h-10 flex items-center justify-center 
                            <?= $item['active'] ? "bg-black text-white" : "bg-neutral-100 text-neutral-500" ?>
                            ">
                            <?= $item['active'] ? $heroicons->checkHtml() : $counter ?>
                        </span>
                        <span class="mt-2 text-center <?= $item['active'] ? "" : "text-neutral-858585" ?>">
                            <?= $this->_escaper->escapeHtml($item['label']) ?>
                        </span>
                    </span>
                </li>
                <?php $counter++; ?>
            <?php endforeach ?>
        </ul>
    <?php endif ?>

    <?php if ($statusMessage): ?>
        <div class="mst-rma-box mst-rma__status">
            <?= /* @noEscape */ $statusMessage ?>
        </div>
    <?php endif; ?>

    <?= $block->getChildHtml('rma.view.buttons') ?>

    <?= $block->getChildHtml('rma.view.info') ?>

    <div class="mst-rma-view__orders mb-8">
        <?php foreach ($orders as $order): ?>
            <?php if ($order): ?>
                <div class="_no-border">
                    <div class="mst-rma-box _no-border">
                        <div class="mst-rma-box__title">
                            <?php if ($isMultiorders): ?>
                                <?= $this->_escaper->escapeHtml(__('RMA Items (order %1)', $block->getOrderLabel($order))) ?>
                            <?php else: ?>
                                <?= $this->_escaper->escapeHtml(__('RMA Items')) ?>
                            <?php endif ?>
                        </div>

                        <div class="mst-rma-box__content">
                            <?php
                            $itemsBlock = $order->getIsOffline()
                                ? $block->getChildBlock('rma.view.offline.items')
                                : $block->getChildBlock('rma.view.items');
                            $itemsBlock->setOrder($order);
                            ?>
                            <?= ($itemsBlock->toHtml()) ?>
                        </div>
                    </div>
                </div>
            <?php endif ?>
        <?php endforeach ?>
    </div>

    <div class="mst-rma-block _align-top border flex flex-col lg:flex-row">
        <div class="lg:flex-[0_0_50%] p-4">
            <?= $block->getChildHtml('rma.view.message') ?>
        </div>
        <div class="lg:flex-[0_0_50%] p-4 max-lg:border-t lg:border-l">
            <?= $block->getChildHtml('rma.view.history') ?>
        </div>
    </div>
</div>
