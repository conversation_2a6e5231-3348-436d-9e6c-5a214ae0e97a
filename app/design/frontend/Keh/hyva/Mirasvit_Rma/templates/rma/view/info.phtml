<?php
declare(strict_types=1);

use Mirasvit\Rma\Block\Rma\View\Info;
use Magento\Framework\Escaper;

/** @var Info $block */
/** @var Escaper $escaper */

$rma    = $block->getRma();
$orders = $block->getOrders();
$cols = $block->getReturnAddressHtml($rma) ? "grid-cols-3" : "grid-cols-2";
?>

<div class="mst-rma-block grid gap-3 <?= $escaper->escapeHtmlAttr($cols)  ?> border my-8">
    <div class="mst-rma-box mst-rma__return p-4">
        <div class="mst-rma-box__title mb-4"><?= $escaper->escapeHtml(__('Request Information')) ?></div>
        <div class="mst-rma-box__content">
            <table class="w-full">
                <tbody>
                    <tr class="max-lg:block">
                        <th scope="col" class="max-lg:block text-left align-top"><?= $escaper->escapeHtml(__('RMA')) ?>:</th>
                        <td class="max-lg:block align-bottom">
                            #<?= $escaper->escapeHtml($rma->getIncrementId()) ?>&nbsp;
                            <span class="mst-rma-status status-branch-<?= $escaper->escapeHtml($block->getStatus($rma)->getColor()) ?>">
                                <?= $escaper->escapeHtml($block->getStatus($rma)->getName()) ?>
                            </span>
                        </td>
                    </tr>
                    <tr class="max-lg:block">
                        <th scope="col" class="max-lg:block text-left align-top"><?= $escaper->escapeHtml(__('Order')) ?>:</th>
                        <td class="max-lg:block align-bottom">
                            <?php foreach ($orders as $order): ?>
                                <div>
                                    <?php if ($rma->getCustomerId()): ?>
                                        <?= /* @noEscape */ $block->getOrderLabel($order, $block->getOrderUrl($order->getId())) ?>
                                    <?php else: ?>
                                        <?= /* @noEscape */ $block->getOrderLabel($order) ?>
                                    <?php endif ?>
                                </div>
                            <?php endforeach; ?>
                            <?= $rma->getIsGift() ? $escaper->escapeHtml(__('(This was a gift)')) : '' ?>
                        </td>
                    </tr>

                    <tr class="max-lg:block">
                        <th scope="col" class="max-lg:block text-left align-top"><?= $escaper->escapeHtml(__('Date Requested')) ?>:</th>
                        <td class="max-lg:block align-bottom"><?= $escaper->escapeHtml($block->formatDate($rma->getCreatedAt(), \IntlDateFormatter::MEDIUM)) ?></td>
                    </tr>

                    <?php foreach ($block->getCustomFields($rma) as $field): ?>
                        <?php if (!$value = $block->getRmaFieldValue($rma, $field)) {
                            continue;
                        } ?>
                        <tr class="max-lg:block">
                            <th scope="col" class="max-lg:block text-left align-top"><?= $escaper->escapeHtml(__($field->getName())) ?>:</th>
                            <td class="max-lg:block align-bottom">
                                <?= $escaper->escapeHtml($value) ?>
                            </td>
                        </tr>
                    <?php endforeach ?>
                </tbody>
            </table>
        </div>
    </div>

    <div class="mst-rma-box mst-rma__contact p-4 border-l">
        <div class="mst-rma-box__title mb-4"><?= $escaper->escapeHtml(__('Contact Information')) ?></div>
        <div class="mst-rma-box__content">
            <address>
                <?= /* @noEscape */ nl2br((string) $block->getShippingAddressHtml($rma)) ?>
            </address>
        </div>
    </div>

    <?php if ($address = $block->getReturnAddressHtml($rma)): ?>
        <div class="mst-rma-box mst-rma__return-address p-4 border-l">
            <div class="mst-rma-box__title mb-4"><?= $escaper->escapeHtml(__('Return Address')) ?></div>
            <div class="mst-rma-box__content">
                <address>
                    <?= /* @noEscape */ nl2br((string) $address) ?>
                </address>
            </div>
        </div>
    <?php endif ?>
</div>
