.account {
    .page-title-wrapper {
        @apply px-0;
    }
}

.password-strength-wrapper {
    @apply relative text-sm bg-neutral-500 w-full p-2;

    &:before {
        @apply absolute block left-0 top-0 h-full z-10 content-[''];
    }

    &:has(.password-weak) {
        &:before {
            @apply bg-rose-500 w-1/4;
        }
    }

    &:has(.password-medium) {
        &:before {
            @apply bg-amber-400 w-1/2;
        }
    }

    &:has(.password-strong) {
        &:before {
            @apply bg-green-400 w-3/4;
        }
    }

    &:has(.password-very-strong) {
        &:before {
            @apply bg-green-400 w-full;
        }
    }
}

.password-strength-meter {
    @apply relative z-20;
}

.order-date {
    @apply text-sm;
}

.order-links {
    @apply block items-center md:border-b md:border-neutral-400;

    .nav {
        @apply text-base p-4 inline-block cursor-pointer whitespace-nowrap;

        &.current {
            @apply bg-black text-white uppercase flex-grow;

        }
    }

    strong {
        @apply font-normal;
    }

    a {
        @apply no-underline;
    }
}

.sales-order-view {
    .order-items {
        .price-excluding-tax,
        .price-including-tax {
            span {
                @apply font-normal text-sm lg:text-base;
            }
        }
    }
}

.account-nav {
    .title {
        @apply uppercase;
    }

    li {
        @apply py-0.5;

    }

    a,
    strong {
        @apply flex justify-between uppercase font-medium;
    }

    a:hover {
        @apply text-keh;
    }

    strong {
        @apply text-blue-600;
    }
}

.actions-toolbar {
    @apply my-2 py-2 flex justify-start;

    & a.back {
        @apply text-secondary-darker underline;
    }

    @layer components {
        & .primary button {
            @apply btn btn-primary btn-size-m;
        }
    }
}
