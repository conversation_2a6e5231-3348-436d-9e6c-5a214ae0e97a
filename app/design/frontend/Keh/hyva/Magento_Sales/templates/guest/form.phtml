<?php

use Magento\Framework\Escaper;
use Magento\Framework\View\Helper\SecureHtmlRenderer;

/** @var Escaper $escaper */
/** @var SecureHtmlRenderer $secureRenderer */
?>
<div class="card max-w-3xl mx-auto">
    <div class="block text-center mb-1 lg:mb-4">
        <h1 class="text-2xl md:text-4xl font-bold">
            <span data-ui-id="page-title-wrapper"><?= $escaper->escapeHtml(__('Track the status of your order')) ?></span>
        </h1>
    </div>
    <form class="form form-orders-search"
          id="oar-widget-orders-and-returns-form"
          action="<?= $escaper->escapeUrl($block->getActionUrl()) ?>"
          method="post" name="guest_post"
          x-data="initForm()">
        <fieldset class="fieldset py-2">
            <?= $block->getBlockHtml('formkey') ?>
            <p class="text-base"><span><?= $escaper->escapeHtml(__('Want to know status of your order or track items your purchased?')) ?></span></p>
            <p class="text-base mb-4"><span><?= $escaper->escapeHtml(__('Enter your order number with your contact below')) ?></span></p>
            <div class="grid">
                <div class="field mb-4 required">
                    <label class="label" for="oar-order-id"><span><?= $escaper->escapeHtml(
                        __('Order number')
                    ) ?></span></label>
                    <div class="control">
                        <input type="text" class="form-input w-full" id="oar-order-id" name="oar_order_id"
                               required/>
                    </div>
                </div>
                <div class="field mb-4 required">
                    <label class="label" for="quick-search-type-id">
                        <span><?= $escaper->escapeHtml(__('Find Order By')) ?></span>
                    </label>
                    <div class="control">
                        <select name="oar_type" id="quick-search-type-id" class="form-select w-full"
                                @change="authenticateBy = event.target.value">
                            <option value="email"><?= $escaper->escapeHtml(__('Email')) ?></option>
                            <option value="zip"><?= $escaper->escapeHtml(__('ZIP Code')) ?></option>
                        </select>
                    </div>
                </div>
                <div class="field mb-4 required">
                    <label class="label" for="oar-billing-lastname">
                        <span><?= $escaper->escapeHtml(__('Billing Last Name')) ?></span>
                    </label>
                    <div class="control">
                        <input type="text" class="form-input w-full" id="oar-billing-lastname"
                               name="oar_billing_lastname"
                               required/>
                    </div>
                </div>
                <div class="field mb-4 required" x-show="authenticateBy !== 'zip'">
                    <label class="label" for="oar_email"><span><?= $escaper->escapeHtml(
                        __('Email')
                    ) ?></span></label>
                    <div class="control">
                        <input type="email" class="form-input w-full"
                               id="oar_email" name="oar_email" :required="authenticateBy !== 'zip'"
                        />
                    </div>
                </div>
                <div class="field mb-4 required" x-show="authenticateBy === 'zip'">
                    <label class="label" for="oar_zip"><span><?= $escaper->escapeHtml(
                        __('Billing ZIP Code')
                    ) ?></span></label>
                    <div class="control">
                        <input type="text" class="form-input w-full"
                               id="oar_zip" name="oar_zip" :required="authenticateBy === 'zip'"
                        />
                    </div>
                </div>
            </div>
        </fieldset>
        <div class="actions-toolbar">
            <div class="primary">
                <button type="submit" title="<?= $escaper->escapeHtml(__('Continue')) ?>"
                        class="action submit primary min-w-[200px]">
                    <span><?= $escaper->escapeHtml(__('Check status')) ?></span>
                </button>
            </div>
        </div>
    </form>
    <script>
        function initForm() {
            return {
                authenticateBy: 'email'
            }
        }
    </script>
</div>
