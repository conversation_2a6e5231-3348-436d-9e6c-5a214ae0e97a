
<?php

use Magento\Framework\View\Element\Template;
use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\Modal;

/**
 * @var Template $block
 * @var ViewModelRegistry $viewModels
 * @var Modal $modalViewModel
 */

$modalViewModel = $viewModels->require(Modal::class);
$modalId = 'shipment-tracking-modal';
?>

<div x-data="{
    ...hyva.modal(),
    url: '',
    isLoading: false,
    iframeHeight: 500,
    minHeight: 400,
    maxHeight: 800,

    init() {
        window.openMyDialog = function (url) {
            this.setUrl(url);
            this.show('shipment-tracking-modal');
        }.bind(this);
    },

    setUrl(newUrl) {
        if (this.url !== newUrl) {
            this.url = newUrl;
            this.isLoading = true;
            this.iframeHeight = this.minHeight;
        }
    },

    handleIframeLoad() {
        this.isLoading = false;
        this.adjustHeight();
    },

    adjustHeight() {
        try {
            const iframe = this.$refs.trackingIframe;
            if (iframe && iframe.contentDocument) {
                const contentHeight = iframe.contentDocument.body.scrollHeight;
                if (contentHeight > 0) {
                    this.iframeHeight = Math.max(this.minHeight, Math.min(contentHeight + 20, this.maxHeight));
                }
            }
        } catch (e) {
            console.log('Cannot access iframe content for height adjustment (cross-origin)');
            this.iframeHeight = this.minHeight;
        }
    },

    handleIframeError() {
        this.isLoading = false;
        this.iframeHeight = this.minHeight;
    }
}" x-init="init()">
    <?= $modalViewModel->createModal()
        ->withDialogRefName($modalId)
        ->initiallyHidden()
        ->addDialogClass('p-5 max-w-5xl')
        ->removeDialogClass('p-10')
        ->withContent('
            <div class="flex items-center justify-between pb-4 border-b border-gray-200">
                <h3 class="text-2xl font-bold">
                    ' . __('Track Your Order') . '
                </h3>
                <div class="flex items-center space-x-2">
                    <button @click="hide()"
                            class="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-white transition-colors">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <div class="w-full bg-white relative transition-all duration-300" :style="`height: ${iframeHeight}px`">
               ' . $block->getChildHtml('loading') . '
                <iframe x-ref="trackingIframe"
                        :src="url"
                        class="w-full h-full border-0 min-w-80 sm:min-w-[460px]"
                        frameborder="0"
                        @load="handleIframeLoad()"
                        @error="handleIframeError()"
                        style="display: block;">
                </iframe>
            </div>
        ')
    ?>
</div>
