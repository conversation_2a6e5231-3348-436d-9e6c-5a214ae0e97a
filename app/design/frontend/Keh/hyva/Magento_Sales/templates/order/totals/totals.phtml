<?php
declare(strict_types=1);

use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\Totals;

/* @var Totals $block */
/* @var Escaper $escaper */

/**
 * This template is used to render totals in the storefront.
 * It is not used for order emails, which use Magento_Sales::order/totals.phtml (not part of the hyva-default-theme).
 */
?>
<?php foreach ($block->getTotals() as $code => $total): ?>
    <?php if ($total->getBlockName()): ?>
        <div class="grid grid-cols-4 py-2 pr-2 relative after:absolute after:bottom-0 after:content-[''] after:border-b after:w-full after:md:w-[550px] after:right-0">
            <?= $block->getChildHtml($total->getBlockName(), false) ?>
        </div>
    <?php else: ?>
        <div class="grid grid-cols-4 py-2 pr-2 relative after:absolute after:bottom-0 after:content-[''] after:border-b after:w-full after:md:w-[550px] after:right-0">
            <div class="col-span-3 text-right" <?= $block->getLabelProperties() ?>>
                <?php if ($total->getStrong()): ?>
                    <strong><?= $escaper->escapeHtml($total->getLabel()) ?></strong>
                <?php else: ?>
                    <?= $escaper->escapeHtml($total->getLabel()) ?>
                <?php endif ?>
            </div>
            <div class="text-right" <?= $block->getValueProperties() ?> data-th="<?= $escaper->escapeHtmlAttr($total->getLabel()) ?>">
                <?php if ($total->getStrong()): ?>
                    <strong><?= $block->formatValue($total) ?></strong>
                <?php else: ?>
                    <?= $block->formatValue($total) ?>
                <?php endif?>
            </div>
        </div>
    <?php endif; ?>
<?php endforeach?>
