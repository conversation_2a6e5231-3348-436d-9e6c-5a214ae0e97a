<?php
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Framework\Escaper;
use Magento\GiftMessage\Helper\Message as GiftMessageHelper;
use Magento\Sales\Block\Order\Items;

/** @var Items $block */
/** @var Escaper $escaper */

$helper = $this->helper(GiftMessageHelper::class);
$cols = $block->getData('is_context_shipment') ? 5 : 6;

$th_css_class = $block->getThCssClass() ?? '';
$tr_css_class = $block->getTrCssClass() ?? '';
?>
<div class="order-items">
    <?php if ($block->isPagerDisplayed()): ?>
        <?= $block->getPagerHtml() ?>
    <?php endif ?>
    <div class="hidden md:grid grid-cols-<?= $escaper->escapeHtmlAttr($cols) ?> text-sm bg-neutral-450 mt-2">
        <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?> col-span-3"><?= $escaper->escapeHtml(__('Product Name')) ?></div>
        <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>"><?= $escaper->escapeHtml(__('SKU')) ?></div>
        <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>"><?= $escaper->escapeHtml(__('Qty')) ?></div>
        <?php if (!$block->getData('is_context_shipment')): ?>
            <div class="<?= $escaper->escapeHtmlAttr($th_css_class) ?>"><?= $escaper->escapeHtml(__('Subtotal')) ?></div>
        <?php endif; ?>
    </div>
    <?php $items = $block->getOrder()->getAllItems(); ?>
    <?php $giftMessage = '' ?>
    <?php foreach ($items as $item): ?>
        <?php
        $parentItem = $item->getParentItem();
        if ($parentItem && $parentItem->getData('product_type') === Configurable::TYPE_CODE) {
            continue;
        }
        ?>
        <div class="<?= $escaper->escapeHtmlAttr($tr_css_class) ?>">
            <?= $block->getItemHtml($item) ?>
        </div>
        <?php if ($helper->isMessagesAllowed('order_item', $item) && $item->getGiftMessageId()): ?>
            <?php $giftMessage = $helper->getGiftMessageForEntity($item); ?>
        <div class="col options mb-2">
            <a href="#"
               id="order-item-gift-message-link-<?= (int)$item->getId() ?>"
               class="action show"
               aria-controls="order-item-gift-message-<?= (int)$item->getId() ?>"
               data-item-id="<?= (int)$item->getId() ?>">
                <?= $escaper->escapeHtml(__('Gift Message')) ?>
            </a>
            <?php $giftMessage =
                $helper->getGiftMessageForEntity($item); ?>
            <div class="order-gift-message" id="order-item-gift-message-<?= (int)$item->getId() ?>" role="region"
                 aria-expanded="false" tabindex="-1">
                <a href="#"
                   title="<?= $escaper->escapeHtml(__('Close')) ?>"
                   aria-controls="order-item-gift-message-<?= (int)$item->getId() ?>"
                   data-item-id="<?= (int)$item->getId() ?>"
                   class="action close">
                    <?= $escaper->escapeHtml(__('Close')) ?>
                </a>
                <dl class="item-options">
                    <dt class="item-sender">
                        <strong class="label">
                            <?= $escaper->escapeHtml(__('From')) ?>
                        </strong>
                        <?= $escaper->escapeHtml($giftMessage->getSender()) ?>
                    </dt>
                    <dt class="item-recipient">
                        <strong class="label">
                            <?= $escaper->escapeHtml(__('To')) ?>
                        </strong>
                        <?= $escaper->escapeHtml($giftMessage->getRecipient()) ?>
                    </dt>
                    <dd class="item-message">
                        <?= $helper->getEscapedGiftMessage($item) ?>
                    </dd>
                </dl>
            </div>
        </div>
    <?php endif ?>
    <?php endforeach; ?>
    <?php if ($block->isPagerDisplayed()): ?>
        <?= $block->getPagerHtml() ?>
    <?php endif ?>
</div>
<div class="flex justify-end mb-2">
    <div class="flex flex-col w-full text-center md:text-right">
        <?= $block->getChildHtml('order_totals') ?>
    </div>
</div>
