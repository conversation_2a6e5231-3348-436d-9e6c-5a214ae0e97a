<?php
use Magento\Framework\Escaper;
use Magento\GiftMessage\Helper\Message;
use Magento\Sales\Block\Order\View;

/** @var View $block */
/** @var Escaper $escaper */

$giftMessageHelper = $this->helper(Message::class);

$order = $block->getOrder();
$isShipment = $block->getData('is_context_shipment');
?>
<div class="order-status md:flex md:justify-between md:flex-wrap md:gap-4 items-center mb-4">
    <div class="mb-4 md:mb-0 text-sm uppercase">
        <?= $block->getChildHtml('order.status') ?>
    </div>
    <div class="lg:inline-block">
        <?= $block->getChildHtml('order.date') ?>
    </div>
    <?= $block->getChildHtml('sales.' . ($isShipment ? 'shipment' : 'order') . '.buttons') ?>
</div>
<div class="order-details-items ordered">
    <?= $block->getChildHtml('order_top_items') ?>

    <div class="py-4 border-t md:border-t-0">
        <?= $block->getChildHtml('order.comments') ?>

        <?php if (!$isShipment): ?>
            <div class="order-title mt-2 mb-3 underline">
                <?php if (!empty($order->getTracksCollection()->getItems())): ?>
                    <?= $block->getChildHtml('tracking-info-link') ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <?= $block->getChildHtml('order_items') ?>

        <div class="mb-4">
            <?= $block->getChildHtml('sales.order.info') ?>
        </div>

        <?php if ($giftMessageHelper->isMessagesAllowed('order', $order) && $order->getGiftMessageId()): ?>
            <div class="block block-order-details-gift-message">
                <div class="block-title"><strong><?= $escaper->escapeHtml(__('Gift Message for This Order')) ?></strong>
                </div>
                <?php
                $giftMessage =
                    $giftMessageHelper->getGiftMessageForEntity($order);
                ?>
                <div class="block-content">
                    <dl class="item-options">
                        <dt class="item-sender">
                            <strong class="label">
                                <?= $escaper->escapeHtml(__('From')) ?>
                            </strong>
                            <?= $escaper->escapeHtml($giftMessage->getSender()) ?>
                        </dt>
                        <dt class="item-recipient">
                            <strong class="label">
                                <?= $escaper->escapeHtml(__('To')) ?>
                            </strong>
                            <?= $escaper->escapeHtml($giftMessage->getRecipient()) ?>
                        </dt>
                        <dd class="item-message">
                            <?= $giftMessageHelper->getEscapedGiftMessage($order) ?>
                        </dd>
                    </dl>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>
<div class="actions-toolbar order-1">
    <div class="secondary">
        <a class="action back" href="<?= $escaper->escapeUrl($block->getBackUrl()) ?>">
            <span><?= $escaper->escapeHtml($block->getBackTitle()) ?></span>
        </a>
    </div>
</div>
