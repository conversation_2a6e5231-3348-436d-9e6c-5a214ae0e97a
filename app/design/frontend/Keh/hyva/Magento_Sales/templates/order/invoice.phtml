<?php
use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\Invoice;

/** @var Invoice $block */
/** @var Escaper $escaper */

$order = $block->getOrder();
?>
<div class="md:flex md:justify-between md:gap-4 items-center mb-4">
        <div class="mb-4 md:mb-0 text-sm uppercase">
            <?= $block->getChildHtml('order.status') ?>
        </div>
        <div class="lg:inline-block">
            <?= $block->getChildHtml('order.date') ?>
        </div>
    <?= $block->getChildHtml('sales.invoice.buttons') ?>
</div>
<div class="order-details-items invoice bg-container-lighter">
    <?= $block->getChildHtml('order_top_items') ?>

    <div class="py-4 border-t md:border-t-0">
        <?= $block->getChildHtml('invoice_items') ?>

        <div class="mb-4">
            <?= $block->getChildHtml('sales.order.info') ?>
        </div>

        <div class="actions-toolbar">
            <div class="secondary">
                <a class="action back" href="<?= $escaper->escapeUrl($block->getBackUrl()) ?>">
                    <span><?= $escaper->escapeHtml($block->getBackTitle()) ?></span>
                </a>
            </div>
        </div>
    </div>
</div>
