<?php
use Hyva\Theme\Model\LocaleFormatter;
use Magento\Framework\Escaper;
use Magento\Sales\Block\Order\Item\Renderer\DefaultRenderer;

/** @var DefaultRenderer $block */
/** @var Escaper $escaper */
/** @var LocaleFormatter $localeFormatter */

$item = $block->getItem();
$cols = $block->getData('is_context_shipment') ? 5 : ($block->getData('is_context_creditmemo') ? 7 : 6);
$td_css_class = 'md:p-3 text-center first:border-t-0 first:text-left last:border-b-0 text-sm md:text-base col max-md:flex max-md:gap-2 max-md:justify-between max-md:text-right max-md:before:content-[attr(data-th)] max-md:p-2 max-md:before:text-left max-md:before:uppercase max-md:before:font-bold [&:not(:last-child)]:max-md:border-b max-md:border-[#e5e7eb]';
?>
<div class="md:border-b md:border-b-[#e5e7eb] md:grid grid-cols-<?= $escaper->escapeHtmlAttr($cols) ?>">
    <div class="<?= $escaper->escapeHtmlAttr($td_css_class) ?> <?= $block->getData('is_context_creditmemo') ? "col-span-2" : "col-span-3" ?>"
        data-th="<?= $escaper->escapeHtml(__('Product Name')) ?>"
    >
        <div>
            <span><?= $escaper->escapeHtml($item->getName()) ?></span>
            <div class="item-options mt-2">
                <?php if ($options = $block->getItemOptions()): ?>
                    <?php foreach ($options as $option): ?>
                        <div class="text-sm flex mt-1">
                            <span><?= $escaper->escapeHtml($option['label']) ?>:</span>
                            <?php if (!$block->getPrintStatus()): ?>
                                <?php $formatedOptionValue = $block->getFormatedOptionValue($option) ?>
                                <span class="ml-1">
                                    <?= $escaper->escapeHtml($formatedOptionValue['value'], ['a']) ?>
                                </span>
                            <?php else: ?>
                                <span class="ml-1">
                                    <?= $escaper->escapeHtml($option['print_value'] ?? $option['value']) ?>
                                </span>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
                <?php /* downloadable */ ?>
                <?php if ($links = $block->getLinks()): ?>
                    <div class="item options my-2">
                        <p><?= $escaper->escapeHtml($block->getLinksTitle()) ?></p>
                        <?php foreach ($links->getPurchasedItems() as $link): ?>
                            <p class="ml-1"><?= $escaper->escapeHtml($link->getLinkTitle()) ?></p>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
                <?php $addtInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
                <?php if ($addtInfoBlock): ?>
                    <?= $addtInfoBlock->setItem($block->getOrderItem())->toHtml() ?>
                <?php endif; ?>
                <?= $escaper->escapeHtml($item->getDescription()) ?>
            </div>
        </div>
    </div>
    <div class="<?= $escaper->escapeHtmlAttr($td_css_class) ?>" data-th="<?= $escaper->escapeHtml(__('SKU')) ?>">
        <span><?= $escaper->escapeHtml($item->getSku()) ?></span>
    </div>
    <div class="<?= $escaper->escapeHtmlAttr($td_css_class) ?>" data-th="<?= $escaper->escapeHtml(__('Qty')) ?>">
        <div>
            <?php if ($block->getData('is_context_invoice') || $block->getData('is_context_creditmemo')): ?>
                <span class="content"><?= /** @noEscape */ $localeFormatter->formatNumber($block->getItem()->getQty()) ?></span>
            <?php endif; ?>
            <?php if ($block->getData('is_context_shipment')): ?>
                <span class="content"><?= /** @noEscape */ $localeFormatter->formatNumber($block->getItem()->getQty()) ?></span>
            <?php endif; ?>
            <?php if ($block->getData('is_context_order')): ?>
                <?php if ($block->getItem()->getQtyOrdered() > 0): ?>
                    <p>
                        <span class="title"><?= $escaper->escapeHtml(__('Ordered')) ?>:</span>
                        <span class="content"><?= /** @noEscape */ $localeFormatter->formatNumber((float)$block->getItem()->getQtyOrdered()) ?></span>
                    </p>
                <?php endif; ?>
                <?php if ($block->getItem()->getQtyShipped() > 0): ?>
                    <p>
                        <span class="title"><?= $escaper->escapeHtml(__('Shipped')) ?>:</span>
                        <span class="content"><?= /** @noEscape */ $localeFormatter->formatNumber($block->getItem()->getQtyShipped()) ?></span>
                    </p>
                <?php endif; ?>
                <?php if ($block->getItem()->getQtyCanceled() > 0): ?>
                    <p>
                        <span class="title"><?= $escaper->escapeHtml(__('Canceled')) ?>:</span>
                        <span class="content"><?= /** @noEscape */ $localeFormatter->formatNumber($block->getItem()->getQtyCanceled()) ?></span>
                    </p>
                <?php endif; ?>
                <?php if ($block->getItem()->getQtyRefunded() > 0): ?>
                    <p>
                        <span class="title"><?= $escaper->escapeHtml(__('Refunded')) ?>:</span>
                        <span class="content"><?= /** @noEscape */ $localeFormatter->formatNumber($block->getItem()->getQtyRefunded()) ?></span>
                    </p>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
    <?php if (!$block->getData('is_context_shipment')): ?>
        <div class="<?= $escaper->escapeHtmlAttr($td_css_class) ?>" data-th="<?= $escaper->escapeHtml(__('Subtotal')) ?>">
            <?= $block->getItemRowTotalHtml() ?>
        </div>
    <?php endif; ?>
    <?php if ($block->getData('is_context_creditmemo')): ?>
        <div class="<?= $escaper->escapeHtmlAttr($td_css_class) ?>" data-th="<?= $escaper->escapeHtml(__('Discount Amount')) ?>">
            <?= $block->getOrder()->formatPrice($item->getDiscountAmount()) ?>
        </div>
        <div class="<?= $escaper->escapeHtmlAttr($td_css_class) ?>" data-th="<?= $escaper->escapeHtml(__('Row Total')) ?>">
            <?= $block->getItemRowTotalAfterDiscountHtml() ?>
        </div>
    <?php endif; ?>
</div>
