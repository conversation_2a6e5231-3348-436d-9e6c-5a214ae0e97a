<?php

use Hyva\Theme\Model\ViewModelRegistry;
use Hyva\Theme\ViewModel\SvgIcons;
use Magento\Framework\View\Element\Template;
use Magento\Framework\Escaper;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var ViewModelRegistry $viewModels */

/** @var SvgIcons $hyvaicons */
$hyvaicons = $viewModels->require(SvgIcons::class);

$loadingText = $block->getData('loading_text');
$showText = $block->getData('show_loading_text') !== false && !empty($loadingText);
$position = $block->getData('position') ?: 'fixed';
?>
<div class="flex <?= $showText ? 'flex-col' : 'flex-row' ?> justify-center items-center w-full h-full <?= $position ?> select-none z-50"
     style="left: 50%;top: 50%;transform: translateX(-50%) translateY(-50%);background: rgba(255,255,255,0.7);"
     x-show="isLoading"
     x-cloak
     x-transition:enter="ease-out duration-200"
     x-transition:enter-start="opacity-0"
     x-transition:enter-end="opacity-100"
     x-transition:leave="ease-in duration-200"
     x-transition:leave-start="opacity-100"
     x-transition:leave-end="opacity-0">
    <img class="max-w-20 <?= $showText ? 'mb-4' : '' ?>"
         src="<?= $block->getViewFileUrl('images/loading.svg') ?>"
         alt="<?= $escaper->escapeHtmlAttr(__('Loading...')) ?>"
         data-pw="keh-loader"
         loading="lazy"/>
    <?php if ($showText): ?>
        <div class="text-center text-gray-600 text-base font-medium">
            <?= $escaper->escapeHtml($loadingText) ?>
        </div>
    <?php endif; ?>
</div>
