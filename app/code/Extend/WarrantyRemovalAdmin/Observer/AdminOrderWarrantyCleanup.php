<?php
declare (strict_types=1);

namespace Extend\WarrantyRemovalAdmin\Observer;

use Extend\Warranty\Model\Product\Type as WarrantyProductType;
use Extend\Warranty\Helper\Tracking;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\Quote;
use Magento\Sales\Model\AdminOrder\Create as OrderCreate;
use Psr\Log\LoggerInterface;

/**
 * Observer to handle warranty cleanup during admin order creation/updates
 * Fixes issue where warranties are not immediately removed when associated products are removed
 * from admin orders
 */
readonly class AdminOrderWarrantyCleanup implements ObserverInterface
{
    /**
     * AdminOrderWarrantyCleanup constructor.
     */
    public function __construct(
        private Tracking $trackingHelper,
        private LoggerInterface $logger,
        private CartRepositoryInterface $cartRepository
    ) {
    }

    /**
     * Triggered after admin order item processing to clean up orphaned warranties
     * Event: adminhtml_sales_order_create_process_item_after
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        try {
            /** @var OrderCreate $orderCreateModel */
            $orderCreateModel = $observer->getData('order_create_model');

            if (!$orderCreateModel) {
                return;
            }

            $quote = $orderCreateModel->getQuote();

            if (!$quote || !$quote->getId()) {
                return;
            }

            // Get all quote items to check for orphaned warranties
            $allItems = $quote->getAllItems();
            $productItems = [];
            $warrantyItems = [];

            // Separate warranty items from regular product items
            foreach ($allItems as $item) {
                if ($item->getProductType() === WarrantyProductType::TYPE_CODE) {
                    $warrantyItems[] = $item;
                } else {
                    $productItems[] = $item;
                }
            }

            // If there are warranty items, check for orphaned ones
            if (!empty($warrantyItems)) {
                $this->cleanupOrphanedWarranties($warrantyItems, $productItems, $quote);
            }

        } catch (LocalizedException $e) {
            $this->logger->error(sprintf(
                '%s: Error in AdminOrderWarrantyCleanup observer - Quote ID: %s, Message: %s',
                __METHOD__,
                isset($quote) ? $quote->getId() : 'null',
                $e->getMessage()
            ), ['exception' => $e]);
        } catch (\Exception $e) {
            $this->logger->error(sprintf(
                '%s: Unexpected error in AdminOrderWarrantyCleanup observer - Quote ID: %s, Message: %s',
                __METHOD__,
                isset($quote) ? $quote->getId() : 'null',
                $e->getMessage()
            ), ['exception' => $e]);
        }
    }

    /**
     * Clean up orphaned warranty items
     *
     * Identifies warranty items whose associated products are no longer in the quote
     * and removes them to maintain data consistency
     *
     * @param array $warrantyItems
     * @param array $productItems
     * @param Quote $quote
     */
    private function cleanupOrphanedWarranties(array $warrantyItems, array $productItems, Quote $quote): void
    {
        $productSkus = [];

        foreach ($productItems as $productItem) {
            $productSkus[] = $productItem->getSku();
        }

        $orphanedWarranties = [];

        foreach ($warrantyItems as $warrantyItem) {
            $associatedProductOption = $warrantyItem->getOptionByCode(WarrantyProductType::ASSOCIATED_PRODUCT);

            if ($associatedProductOption) {
                $associatedProductSku = $associatedProductOption->getValue();

                if (!in_array($associatedProductSku, $productSkus)) {
                    $orphanedWarranties[] = $warrantyItem;

                    if ($this->trackingHelper->isTrackingEnabled()) {
                        $planId = $warrantyItem->getOptionByCode(WarrantyProductType::WARRANTY_ID);
                        $planIdValue = $planId ? $planId->getValue() : '';

                        if ($associatedProductSku && $planIdValue) {
                            $trackingData = [
                                'eventName' => 'trackOfferRemovedFromCart',
                                'productId' => $associatedProductSku,
                                'planId' => $planIdValue,
                            ];
                            $this->trackingHelper->setTrackingData($trackingData);
                        }
                    }
                }
            }
        }

        if (!empty($orphanedWarranties)) {
            foreach ($orphanedWarranties as $orphanedWarranty) {
                $quote->removeItem($orphanedWarranty->getId());
            }

            $quote->collectTotals();
            $this->cartRepository->save($quote);
        }
    }
}
