{"name": "extend/warranty-removal-admin", "description": "Fixes warranty cleanup issues in admin order creation", "type": "magento2-module", "version": "1.0.0", "license": "proprietary", "require": {"php": ">=8.3", "magento/framework": "*", "magento/module-sales": "*", "magento/module-quote": "*", "extend/module-warranty": "*"}, "autoload": {"files": ["registration.php"], "psr-4": {"Extend\\WarrantyRemovalAdmin\\": ""}}}