define([
    'jquery',
    'Inchoo_FedExHoldAtLocation/js/postcode-validator',
    'mage/template',
    'jquery-ui-modules/widget'
], function ($, postCodeValidator, mageTemplate) {
    'use strict';

    $.widget('keh.holdAtLocation', {
        options: {
            token: '',
            orderAddress: {
                city: "",
                country: "",
                postalcode: "",
                state: "",
                street: "",
                token: ""
            },
            locations: [],
            locationsTemplate: mageTemplate('#hal-template'),
            tokenName: 'adminFedexToken',
            checkboxName: 'adminFedexCheckbox',
        },
        _init: function () {
            this.tokenApiUrl = window.location.origin + this.options.tokenApiUrl;
            this.locationsApiUrl = window.location.origin + this.options.locationsApiUrl;

            $(this.options.zipToggleSelector).on('click', $.proxy(this.toggleZipInput, this));
            $(this.options.findLocationsButtonSelector).on('click', $.proxy(this.fetchLocations, this));
            $(document).on('click', 'input[name="fedexLocation"]', $.proxy(this.onLocationClick, this));
            $('#order-shipping_address_country_id').on('change', $.proxy(this.onShippingAddressCountryChange, this));
            $('#order-shipping_address_postcode').on('blur', $.proxy(this.onShippingAddressPostCodeChange, this));

            this.isExpired(this.options.tokenName);
            this.isExpired(this.options.checkboxName);


        },

        getSortedRegions: function (countryCode) {
            const regions = this.options.countries[countryCode].regions || {};
            const options = ['<option value="">Please select</option>'];

            if (regions) {
                Object.entries(regions).map(region => {
                    options.push(`<option value="${region[0]}" title="${region[1].name}">${region[1].name}</option>`);
                });

                $('#order-shipping_address_region_id')
                    .empty()
                    .append(options.join(''));
            }
        },

        onShippingAddressPostCodeChange: function(e) {
            $(this.options.anotherZipCodeSelector).val(e.target.value);
        },

        onLocationClick: function(e) {
            const locationId = e.target.id;
            const locations = this.getValueFromLocalStorage('adminFedexNew_' + this.anotherZipCode + '_' + this.anotherCountryCode, 'key');
            const location = locations.filter(location => location.locationId === locationId)[0];
            
            const name = location.contactAndAddress.contact.companyName;
            const address = location.contactAndAddress.address;
            const city = address.city;
            const postCode = address.postalCode;  
            const countryCode = address.countryCode;                     
            const stateCode = this.getRegionId(this.anotherCountryCode, address.stateOrProvinceCode);
            const street = address.streetLines.join(',')

            if ($('#order-shipping_same_as_billing').prop('checked')) {
                $('#order-shipping_same_as_billing').prop('checked', false);
            }

            // update fields with FedEx values, but keep them disabled
            $('#order-shipping_address_company').val(name);
            $('#order-shipping_address_street0').val(street);
            $('#order-shipping_address_postcode').val(postCode);
            $('#order-shipping_address_city').val(city);
            $('#order-shipping_address_country_id').val(countryCode).trigger('change'); 
            this.getSortedRegions(countryCode);
            $('#order-shipping_address_region_id').val(stateCode);

            // enable other fields to be updated in Admin
            const fields = [
                '#order-shipping_address_prefix',
                '#order-shipping_address_firstname',
                '#order-shipping_address_middlename',
                '#order-shipping_address_lastname',
                '#order-shipping_address_suffix',
                '#order-shipping_address_telephone',
                '#order-shipping_address_fax',
                '#order-shipping_address_vat_id'
            ].join(',');

            $(fields).prop('disabled', false);
            $('#order-shipping_address_vat_id').next().children('button')
                .prop('disabled', false)
                .removeClass('disabled');

            this.manageHiddenField(locationId);
        },

        manageHiddenField(locationId) {
            const id = 'fedex-hidden-field';

            if ($('#' + id).length) {
                $('#' + id).val(locationId);
            } else {
                $('<input>').attr({
                    type: 'hidden',
                    id: id,
                    name: 'order[shipping_address][extension_attribute][fedex_location_id]',
                    value: locationId
                }).insertAfter('#fedex-wrapper');
            }
        },

        toggleZipInput: function () {
            $(this.options.fedexFormSelector).toggle();

            const isChecked = $(this.options.zipToggleSelector).prop('checked');
            this.setValueToLocalStorage(this.options.checkboxName, isChecked);

            if (!isChecked) {
                $('#fedex-hidden-field').remove();
            }
        },

        fetchLocations: function () {
            this.getToken();
            this.getLocations();
        },

        getLocations: function () {
            this.anotherCountryCode = $(this.options.anotherCountrySelector).val();
            this.anotherZipCode = $(this.options.anotherZipCodeSelector).val().trim();

            $(this.options.noLocationsMessageSelector).hide();

            const data = {
                street: [$('#order-shipping_address_street0').val(), $('#order-shipping_address_street1').val()].join(' '),
                city: $('#order-shipping_address_city').val(),
                state: $('#order-shipping_address_region_id').val(), 
                token: this.getValueFromLocalStorage(this.options.tokenName, 'key'),
                country: this.anotherCountryCode,
                postalcode: this.anotherZipCode
            };

            const hashKey = 'adminFedexNew_' + this.anotherZipCode + '_' + this.anotherCountryCode;

            this.isExpired(hashKey);
            
            if (!this.getValueFromLocalStorage(hashKey, "key")) {
                $.ajax({
                    url: this.locationsApiUrl,
                    timeout: this.options.ajaxTimeout,
                    type: "POST",
                    headers: {
                        "Content-Type": "application/json"
                    },
                    // do not remove beforeSend prop, because it prevents adding of formKey as GET param to the request
                    beforeSend: function() {
                        $('#hal-list').empty();
                    },
                    data: JSON.stringify({
                        data: data
                    }),
                    success: $.proxy(function (response) {
                        const fedexResponse = JSON.parse(response);
                        const holdAtLocations = this.filterLocations(fedexResponse.output.locationDetailList);
                        this.options.locations = this.fixPostCodeIssue(holdAtLocations);

                        if (this.options.locations.length === 0) {
                            $(this.options.noLocationsMessageSelector).toggle()
                        } else {
                            this.setValueToLocalStorage(hashKey, this.options.locations)
                            this.renderLocations();
                        }
                    }, this),
                    error: function (xhr, status, errorThrown) {
                        console.error(xhr, status, errorThrown);
                    }
                });                
            } else {
                this.options.locations = this.getValueFromLocalStorage(hashKey, "key");
                this.renderLocations();
            }  
        },

        isExpired: function (data) {
            const expiresAt = this.getValueFromLocalStorage(data, 'expiry');
            const now = Date.now();
            
            if (expiresAt < now) {
                localStorage.removeItem(data);
            }
        },

        renderLocations: function() {
            const renderLocations = [];

            this.options.locations.map(location => {
                const id = location.locationId;
                const name = location.contactAndAddress.contact.companyName;
                const address = location.contactAndAddress.address;
                const city = address.city;
                const postCode = address.postalCode;
                const stateCode = this.getRegionName(this.anotherCountryCode, address.stateOrProvinceCode);
                const street = address.streetLines.join(',')
                const distance = location.distance;

                const renderLocation = {
                    id: id,
                    name: name,
                    street: street,
                    city: city,
                    postCode: postCode,
                    stateCode: stateCode,
                    distance: this.parseDistance(distance)
                }

                renderLocations.push(renderLocation);
            });
            

            var html = this.options.locationsTemplate({
                renderLocations: renderLocations
            });
            $('#hal-list').html(html);
        },

        parseDistance: function (distance) {
            return distance.value + ' ' + distance.units.toLowerCase();
        },

        useValidZipCode: function (location, countryCode) {
            if (location.contactAndAddress.addressAncillaryDetail.hasOwnProperty('locationInCity') &&
                location.contactAndAddress.addressAncillaryDetail.locationInCity.length > 0 &&
                postCodeValidator.validate(location.contactAndAddress.addressAncillaryDetail.locationInCity, countryCode, this.options.postalCodePatterns)) {
                return location.contactAndAddress.addressAncillaryDetail.locationInCity
            } else {
                location.contactAndAddress.addressAncillaryDetail.locationInCity = '';

                return location.contactAndAddress.address.postalCode
            }
        },

        fixPostCodeIssue: function (locations) {
            locations.map(location => {
                location.contactAndAddress.address.postalCode = this.useValidZipCode(location, this.anotherCountryCode);
            })

            return locations
        },

        filterLocations: function(locations) {
            return locations.filter(function (location) {
                return location.locationCapabilities.some(function (capability) {
                    return capability.transferOfPossessionType === 'HOLD_AT_LOCATION'
                });
            });
        },

        getToken: function () {
            this.isExpired(this.options.tokenName);

            if (this.options.token = this.getValueFromLocalStorage(this.options.tokenName, 'key')) {
                console.log('Token already exists in local storage, skipping API call.');
                return
            }
            
            $.ajax({
                type: 'GET',
                url: this.options.tokenApiUrl,
                timeout: this.options.ajaxTimeout,
                success: $.proxy(function (response) {
                    this.setValueToLocalStorage(this.options.tokenName, response);
                }, this),
                error: function (xhr, status, errorThrown) {
                    console.error(xhr, status, errorThrown);
                }
            });
        },

        setValueToLocalStorage: function (name, data) {
            localStorage.setItem(name, JSON.stringify({
                key: data,
                expiry: Date.now() + this.options.ttl
            }))
        },

        getValueFromLocalStorage: function (name, key) {
            var data = JSON.parse(localStorage.getItem(name));

            if (data && data.hasOwnProperty(key)) {
                return data[key]
            }

            return false
        },

        getRegionName: function (countryCode, regionCode) {
            regionCode = this.checkRegionCode(countryCode, regionCode);

            try {
                var regionName = '';

                var regions = this.options.countries[countryCode] ?
                    this.options.countries[countryCode].regions
                    : null;

                if (regions) {
                    Object.entries(regions).forEach(function (region) {
                        if (region[1]['code'] === regionCode) {
                            regionName = region[1]['name'];
                        }
                    })
                }

                return regionName;
            } catch (e) {
                throw new Error('Failed to find region name for: ("'+countryCode+'", "'+regionCode+'")')
            }
        },

        getRegionId: function (countryCode, regionCode) {
            regionCode = this.checkRegionCode(countryCode, regionCode);

            const regions = this.options.countries[countryCode] ?
                    this.options.countries[countryCode].regions
                    : null;

            const result = Object.entries(regions).filter(region => region[1].code === regionCode)

            if (result.length) {
                return result[0][0]
            }

            return
        },

        checkRegionCode: function (countryCode, stateCode) {
            if (countryCode === 'CA') {
                // https://en.wikipedia.org/wiki/Canadian_postal_abbreviations_for_provinces_and_territories#Names_and_abbreviations
                switch (stateCode) {
                    // Alberta
                    case 'AB':
                        break;
                    case 'Alb.':
                        stateCode = 'AB';
                        break;

                    //  British Columbia
                    case 'BC':
                        break;
                    case 'C.-B.':
                        stateCode = 'BC';
                        break;

                    //  Manitoba
                    case 'MB':
                        break;
                    case 'Man.':
                        stateCode = 'MB';
                        break;

                    //  New Brunswick
                    case 'NB':
                        break;
                    case 'N.-B.':
                        stateCode = 'NB';
                        break;

                    //  Newfoundland and Labrador
                    case 'NL':
                        break;
                    case 'T.-N.-L.':
                    case 'NF':
                        stateCode = 'NL';
                        break;

                    //  Northwest Territories
                    case 'NT':
                        break;
                    case 'T.N.-O.':
                        stateCode = 'NT';
                        break;

                    // Nova Scotia
                    case 'NS':
                        break;
                    case 'N.-É.':
                        stateCode = 'NS';
                        break;

                    // Nunavut
                    case 'NU':
                        break;
                    case 'Nt':
                        stateCode = 'NU';
                        break;

                    // Ontario
                    case 'ON':
                        break;
                    case 'Ont':
                        stateCode = 'ON';
                        break;

                    // Prince Edward Island
                    case 'PE':
                        break;
                    case 'Î.-P.-É.':
                        stateCode = 'PE';
                        break;

                    // Quebec
                    case 'QC':
                        break;
                    case 'PQ':
                        stateCode = 'QC';
                        break;

                    // Saskatchewan
                    case 'SK':
                        break;
                    case 'Sask.':
                        stateCode = 'SK';
                        break;

                    // Yukon
                    case 'YT':
                        break;
                    case 'Yn':
                        stateCode = 'YT';
                        break;

                    default:
                        console.error('Unknown Canadian region code:', stateCode);
                        this.validateShippingForm();
                }
            }

            return stateCode
        },
    });

    return $.keh.holdAtLocation;
});
