<?php

use Magento\Framework\Escaper;
use Magento\Framework\View\Element\Template;
use Inchoo\FedExHoldAtLocation\ViewModel\Admin\HoldAtLocation;

/** @var Template $block */
/** @var Escaper $escaper */
/** @var HoldAtLocation $halData */

$halData = $block->getData('hal_view_model');
?>

<div id="fedex-wrapper">
    <div class="admin-fieldset" id="fedex-toggle">
        <div class="admin__field admin__field-option zip-toggle">
            <input name="zip-toggle" type="checkbox" id="zip-toggle" class="admin__control-checkbox" />
            <label for="zip-toggle" class="admin__field-label"><?= $escaper->escapeHtml(__('Use Hold At Location')) ?></label>
        </div>
    </div>

    <div class="admin-fieldset" id="fedex-form" style="display: none;">
        <br>
        <div class="admin__field field-another-zip-code">
            <label class="label admin__field-label" for="another-zip-code"><?= $escaper->escapeHtml(__('Zip Code')) ?></label>
            <div class="admin__field-control value">
                <input id="another-zip-code" name="another-zip-code" class="input-text admin__control-text" type="text" />
            </div>
        </div>

         <div class="admin__field field-another-country">
             <label class="label admin__field-label" for="another-country"><?= $escaper->escapeHtml(__('Another Country')) ?></label>
             <div class="admin__field-control control">
                <select id="another-country" name="another-country" class="required-entry required-entry _required select admin__control-select" style="width: 100%;">
                    <?php foreach ($halData->getCountries() as $country): ?>
                        <option 
                            <?php if ($country['value'] === 'US'): ?>
                            selected
                            <?php endif?>
                        value="<?= $country['value'] ?>"><?= $country['label'] ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>

        <br>
        <div class="actions">
            <button type="button" class="action-default scalable" id="hal-button"
                title="<?= $escaper->escapeHtml(__('Find Locations')) ?>">
                <?= $escaper->escapeHtml(__('Find Locations')) ?>
            </button>
        </div>

        <div id="hal-list"></div>
        <script type="text/x-magento-template" id="hal-template">
            <ul class="hal-locations">
                <% _.each(renderLocations, function(location) { %>
                    <li class="admin__field-option hal-location">
                        <input name="fedexLocation" type="radio" value="freeshipping_freeshipping"  id="<%- location.id %>" class="admin__control-radio required-entry">
                        <label class="admin__field-label" for="<%- location.id %>">
                             <strong><%- location.name %></strong> <small>(<%- location.distance %>)</small>
                        </label>

                        <div class="hal-address">
                            <span><%- location.street %></span><br>
                            <span><%- location.city %>, <%- location.stateCode %> <%- location.postCode %></span>
                        </div>
                    </li>
                <% }); %>
            </ul>
        </script>
        <style>
            .hal-locations {
                list-style: none;
            }

            .hal-location:not(:last-child) {
                margin-bottom: 16px
            }

            .hal-address {
                padding-left: 26px;
                margin-top: 7px;
            }
            
            #hal-list:not(:empty) {
                margin-top: 32px;
            }
        </style>

        <p id="no-locations-msg" style="display: none;">
            <br>
            <?= $escaper->escapeHtml(__('No results found. Please check the ZIP/Postal Code and try again.')) ?></p>
    </div>
</div>

<script type="text/x-magento-init">
    {
        "#fedex-wrapper": {
            "hold-at-location": {
                "zipToggleSelector": "#zip-toggle",
                "fedexFormSelector": "#fedex-form",
                "anotherZipCodeSelector": "#another-zip-code",
                "anotherCountrySelector": "#another-country",
                "findLocationsButtonSelector": "#hal-button",
                "tokenApiUrl": "/rest/V1/fedex/oauth/token",
                "locationsApiUrl": "/rest/V1/fedex/locations",
                "ajaxTimeout": 10000,
                "ttl": 3600000 <?php /** 1 hour */ ?>,
                "noLocationsMessageSelector": "#no-locations-msg",
                "postalCodePatterns": <?= json_encode($halData->getPostCodes()) ?>,
                "countries": <?= json_encode($halData->getRegions()) ?>
            }
        }
    }
</script>
