<?php

namespace Magestore\SupplierSuccess\Model\Supplier;

use Magento\Backend\Model\Session;
use Magento\Directory\Model\Country;
use Magento\Directory\Model\Region;
use Magento\Directory\Model\ResourceModel\Country\CollectionFactory;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\File\Csv;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Driver\File;
use Magento\Framework\Filesystem\File\WriteFactory;
use Magento\Framework\Message\ManagerInterface;
use Magestore\SupplierSuccess\Api\Data\SupplierInterface;
use Magestore\SupplierSuccess\Api\SupplierRepositoryInterface;
use Magestore\SupplierSuccess\Model\SupplierFactory;

/**
 * Class for CSV import handle
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 * @SuppressWarnings(PHPMD.CookieAndSessionMisuse)
 */
class CsvImportHandler
{
    const STATUS_ENABLE_TEXT = 'Enable';
    const STATUS_DISABLE_TEXT = 'Disable';
    const STATUS_ENABLE = 1;
    const STATUS_DISABLE = 0;

    /**
     * @var \Magento\Framework\File\Csv
     */
    protected $csvProcessor;

    /**
     * @var
     */
    protected $request;

    /**
     * @var \Magento\Framework\Message\ManagerInterface
     */
    protected $messageManager;

    /**
     * @var \Magento\Framework\Filesystem
     */
    protected $filesystem;

    /**
     * @var \Magento\Backend\Model\Session
     */
    protected $backendSession;

    /**
     * @var \Magento\Framework\Filesystem\File\WriteFactory
     */
    protected $fileWriteFactory;

    /**
     * @var \Magento\Framework\Filesystem\Driver\File
     */
    protected $driverFile;

    /**
     * @var SupplierFactory
     */
    protected $supplierFactory;

    /**
     * @var SupplierRepositoryInterface
     */
    protected $supplierRepository;

    /**
     * @var \Magento\Directory\Model\ResourceModel\Country\CollectionFactory
     */
    protected $countryCollectionFactory;

    /**
     * @var array
     */
    protected $countries;

    /**
     * CsvImportHandler constructor.
     *
     * @param Csv $csvProcessor
     * @param ManagerInterface $messageManager
     * @param Filesystem $filesystem
     * @param Session $backendSession
     * @param WriteFactory $fileWriteFactory
     * @param File $driverFile
     * @param SupplierFactory $supplierFactory
     * @param SupplierRepositoryInterface $supplierRepository
     * @param CollectionFactory $countryCollectionFactory
     */
    public function __construct(
        \Magento\Framework\File\Csv $csvProcessor,
        \Magento\Framework\Message\ManagerInterface $messageManager,
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Backend\Model\Session $backendSession,
        \Magento\Framework\Filesystem\File\WriteFactory $fileWriteFactory,
        \Magento\Framework\Filesystem\Driver\File $driverFile,
        SupplierFactory $supplierFactory,
        SupplierRepositoryInterface $supplierRepository,
        \Magento\Directory\Model\ResourceModel\Country\CollectionFactory $countryCollectionFactory
    ) {
        $this->csvProcessor = $csvProcessor;
        $this->messageManager = $messageManager;
        $this->filesystem = $filesystem;
        $this->backendSession = $backendSession;
        $this->driverFile = $driverFile;
        $this->fileWriteFactory = $fileWriteFactory;
        $this->supplierFactory = $supplierFactory;
        $this->supplierRepository = $supplierRepository;
        $this->countryCollectionFactory = $countryCollectionFactory;
    }

    /**
     * Import from csv file
     *
     * @param array $file
     * @return array
     * @throws \Magento\Framework\Exception\AlreadyExistsException
     * @throws \Magento\Framework\Exception\FileSystemException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @SuppressWarnings(PHPMD.NPathComplexity)
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function importFromCsvFile(array $file): array
    {
        if (!isset($file['tmp_name'])) {
            throw new \Magento\Framework\Exception\LocalizedException(__('Invalid file upload attempt.'));
        }
        if ($file['type'] !== 'text/csv') {
            throw new LocalizedException(__("Invalid file format"));
        }
        $importSupplierRawData = $this->csvProcessor->getData($file['tmp_name']);
        $fileFields = $importSupplierRawData[0];
        // validate fields is missing or not
        $this->validateMissingFields($fileFields);
        // validate body
        $validFields = $this->filterFileFields($fileFields);
        $invalidFields = array_diff_key($fileFields, $validFields);
        $importSupplierData = $this->filterImportSupplierData($importSupplierRawData, $invalidFields, $validFields);
        $supplierArray = [];
        $importSuccess = 0;
        $editSuccess = 0;
        $mappedSupplierCode = [];
        foreach ($importSupplierData as $rowIndex => $dataRow) {
            // skip headers
            if ($rowIndex == 0) {
                continue;
            }
            // skip empty row
            if ($this->isEmptyRow($dataRow)) {
                continue;
            }
            $this->isValidRequiredCells($dataRow);
            if (array_contains($mappedSupplierCode, $dataRow[0])) {
                throw new LocalizedException(__("Supplier Code must not be duplicated in the file"));
            }
            $supplierArray[] = $this->mappingColumn($dataRow);
            $mappedSupplierCode[] = $dataRow[0];
        }
        foreach ($supplierArray as $supplier) {
            $supplierEntity = $this->supplierRepository->getByCode($supplier[SupplierInterface::SUPPLIER_CODE]);
            if ($supplierEntity) {
                $supplier[SupplierInterface::SUPPLIER_ID] = $supplierEntity->getId();
                $updateEntity = $this->supplierFactory->create();
                $updateEntity->setData($supplier);
                $this->supplierRepository->save($updateEntity);
                $editSuccess++;
            } else {
                $supplier[SupplierInterface::SUPPLIER_ID] = null;
                $addEntity = $this->supplierFactory->create();
                $addEntity->setData($supplier);
                $this->supplierRepository->save($addEntity);
                $importSuccess++;
            }
        }

        return [
            'import_success' => $importSuccess,
            'edit_success' => $editSuccess
        ];
    }

    /**
     * Filter file fields (i.e. unset invalid fields)
     *
     * @param array $fileFields
     * @return string[] filtered fields
     */
    public function filterFileFields(array $fileFields)
    {
        $filteredFields = $this->getCsvFields();
        $requiredFieldsNum = count($this->getCsvFields());
        $fileFieldsNum = count($fileFields);

        // process title-related fields that are located right after required fields with store code as field name)
        for ($index = $requiredFieldsNum; $index < $fileFieldsNum; $index++) {
            $titleFieldName = $fileFields[$index];
            $filteredFields[$index] = $titleFieldName;
        }

        return $filteredFields;
    }

    /**
     * Get required csv fields
     *
     * @return array
     */
    public function getCsvFields(): array
    {
        // indexes are specified for clarity, they are used during import
        return [
            0 => __('SUPPLIER CODE'),
            1 => __('SUPPLIER NAME'),
            2 => __('CONTACT PERSON'),
            3 => __('SUPPLIER EMAIL'),
            4 => __('CC EMAILS'),
            5 => __('DESCRIPTION'),
            6 => __('STATUS'),
            7 =>__('TELEPHONE'),
            8 => __('FAX'),
            9 => __('STREET'),
            10 => __('CITY'),
            11 => __('COUNTRY'),
            12 => __('REGION'),
            13 =>__('ZIP/POSTAL CODE'),
            14 => __('WEBSITE')
        ];
    }

    /**
     * Is validate cell
     *
     * @param array $data
     * @return void
     * @throws LocalizedException
     */
    public function isValidRequiredCells(array $data)
    {
        $requireFields = [
            0 => SupplierInterface::SUPPLIER_CODE,
            1 => SupplierInterface::SUPPLIER_NAME,
            2 => SupplierInterface::CONTACT_NAME,
            3 => SupplierInterface::CONTACT_EMAIL,
            6 => SupplierInterface::STATUS
        ];
        $errorColumns = [];
        $csvFields = $this->getCsvFields();
        foreach (array_keys($requireFields) as $index) {
            if (empty($data[$index])) {
                $errorColumns[] = $csvFields[$index];
            }
        }
        if (!empty($errorColumns)) {
            $errorMessageColumns = implode(", ", $errorColumns);
            throw new LocalizedException(__("You cannot leave %1 blank!", $errorMessageColumns));
        }
    }

    /**
     * Is empty row
     *
     * @param array $dataArray
     * @return bool
     */
    public function isEmptyRow(array $dataArray): bool
    {
        foreach ($dataArray as $data) {
            if (!empty($data)) {
                return false;
            }
        }
        return true;
    }

    /**
     * Filter import supplier data
     *
     * @param array $supplierRawData
     * @param array $invalidFields
     * @param array $validFields
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    public function filterImportSupplierData(array $supplierRawData, array $invalidFields, array $validFields): array
    {
        $validFieldsNum = count($validFields);
        foreach ($supplierRawData as $rowIndex => $dataRow) {
            // skip empty rows
            if (count($dataRow) <= 1) {
                unset($supplierRawData[$rowIndex]);
                continue;
            }
            // unset invalid fields from data row
            foreach ($dataRow as $fieldIndex => $fieldValue) {
                if (isset($invalidFields[$fieldIndex])) {
                    unset($supplierRawData[$rowIndex][$fieldIndex]);
                }
            }
            // check if number of fields in row match with number of valid fields
            if (count($supplierRawData[$rowIndex]) != $validFieldsNum) {
                throw new \Magento\Framework\Exception\LocalizedException(__('Invalid file format.'));
            }
        }
        return $supplierRawData;
    }

    /**
     * Get base dir media
     *
     * @return \Magento\Framework\Filesystem\Directory\ReadInterface
     */
    public function getBaseDirMedia(): Filesystem\Directory\ReadInterface
    {
        return $this->filesystem->getDirectoryRead('media');
    }

    /**
     * Remapping column to data array
     *
     * @param array $rawData
     * @return array
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @throws LocalizedException
     */
    public function mappingColumn(array $rawData): array
    {
        $data = [];
        $supplierAttributes = [
            SupplierInterface::SUPPLIER_CODE,
            SupplierInterface::SUPPLIER_NAME,
            SupplierInterface::CONTACT_NAME,
            SupplierInterface::CONTACT_EMAIL,
            SupplierInterface::ADDITIONAL_EMAILS,
            SupplierInterface::DESCRIPTION,
            SupplierInterface::STATUS,
            SupplierInterface::TELEPHONE,
            SupplierInterface::FAX,
            SupplierInterface::STREET,
            SupplierInterface::CITY,
            SupplierInterface::COUNTRY_ID,
            SupplierInterface::REGION,
            SupplierInterface::POSTCODE,
            SupplierInterface::WEBSITE
        ];
        foreach ($supplierAttributes as $index => $attribute) {
            switch ($attribute) {
                case SupplierInterface::COUNTRY_ID:
                    $countryName = $rawData[$index];
                    if (!empty($countryName)) {
                        $countryModel = $this->validateAndGetCountry($countryName);
                        if ($countryModel) {
                            $data[$attribute] = $countryModel->getCountryId();
                        } else {
                            throw new LocalizedException(
                                __("You can only select supplier's country from Magento countries list")
                            );
                        }
                    } else {
                        $data[SupplierInterface::COUNTRY_ID] = "";
                    }
                    break;
                case SupplierInterface::REGION:
                    $region = $rawData[$index];
                    if (!empty($region)) {
                        $regionModel = $this->validateAndGetRegion($region, $data[SupplierInterface::COUNTRY_ID]);
                        if (!empty($regionModel)) {
                            $data[$attribute] = $regionModel->getName();
                            $data[SupplierInterface::REGION_ID] = $regionModel->getRegionId();
                        } else {
                            $message = "For some countries, you must select supplier's region from Magento region list";
                            throw new LocalizedException(
                                __($message)
                            );
                        }
                    } else {
                        $data[$attribute] = "";
                        $data[SupplierInterface::REGION_ID] = "";
                    }
                    break;
                case SupplierInterface::STATUS:
                    $status = trim($rawData[$index]);
                    if (!empty($status)) {
                        if ($status === self::STATUS_ENABLE_TEXT) {
                            $data[$attribute] = self::STATUS_ENABLE;
                        } elseif ($status === self::STATUS_DISABLE_TEXT) {
                            $data[$attribute] = self::STATUS_DISABLE;
                        } else {
                            throw new LocalizedException(
                                __("The status of the supplier can only be \"Enable/Disable\"")
                            );
                        }
                    }
                    break;
                default:
                    $data[$attribute] = $rawData[$index];
            }
        }
        return $data;
    }

    /**
     * Validate and get region
     *
     * @param string $regionInput
     * @param string $countryCode
     * @return Region|null
     */
    public function validateAndGetRegion(string $regionInput, string $countryCode): ?Region
    {
        $country = $this->getAllCountries()[$countryCode];
        $regions = $country->getRegions()->getItems();
        if (empty($regions)) {
            $region = ObjectManager::getInstance()->create(\Magento\Directory\Model\Region::class);
            $region->setName($regionInput);
            $region->setCode(null);
            return $region;
        }
        /** @var Region $region */
        foreach ($regions as $region) {
            if (strtolower($region->getName()) === strtolower(trim($regionInput))) {
                return $region;
            }
        }
        return null;
    }

    /**
     * Get country
     *
     * @param string $countryName
     * @return Country|null
     */
    public function validateAndGetCountry(string $countryName): ?Country
    {
        foreach ($this->getAllCountries() as $country) {
            if ($country->getName() !== null && strtolower($country->getName()) === strtolower($countryName)) {
                return $country;
            }
        }
        return null;
    }

    /**
     * Get all countries
     *
     * @return Country[]
     */
    public function getAllCountries(): array
    {
        if (empty($this->countries)) {
            $countryFactory = $this->countryCollectionFactory->create();
            $this->countries = $countryFactory->getItems();
        }
        return $this->countries;
    }

    /**
     * Validate fields
     *
     * @param array $fileFields
     * @return void
     * @throws LocalizedException
     */
    public function validateMissingFields(array $fileFields)
    {
        $requiredCsv = $this->getCsvFields();
        $missingColumns = [];
        foreach ($requiredCsv as $item) {
            $foundColumn = false;
            foreach ($fileFields as $fileField) {
                if (strtolower($item) === strtolower($fileField)) {
                    $foundColumn = true;
                }
            }
            if (!$foundColumn) {
                $missingColumns[] = $item;
            }
        }
        if (!empty($missingColumns)) {
            $columns = implode(", ", $missingColumns);
            throw new LocalizedException(__("Cannot find %1 data in the file", $columns));
        }
    }
}
