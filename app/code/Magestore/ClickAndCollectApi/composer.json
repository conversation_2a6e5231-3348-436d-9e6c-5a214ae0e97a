{"name": "magestore/module-click-and-collect-api", "description": "N/A", "require": {"php": "~7.3.0||~7.4.0", "magento/framework": "*", "magento/module-ui": "*", "magento/module-inventory-admin-ui": "*", "magento/module-inventory-catalog-api": "*", "magento/module-inventory-api": "*", "magestore/webpos-magento2": "*", "magestore/module-core": "*"}, "type": "magento2-module", "license": ["OSL-3.0", "AFL-3.0"], "version": "1.0.0", "authors": [{"name": "magestore", "email": "<EMAIL>", "homepage": "https://www.magestore.com/", "role": "Developer"}], "autoload": {"files": ["registration.php"], "psr-4": {"Magestore\\ClickAndCollectApi\\": ""}}}