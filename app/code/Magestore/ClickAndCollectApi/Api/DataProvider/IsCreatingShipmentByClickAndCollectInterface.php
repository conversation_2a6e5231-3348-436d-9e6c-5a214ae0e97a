<?php
/**
 * Copyright © Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Magestore\ClickAndCollectApi\Api\DataProvider;

/**
 * DataProvider - IsCreatingShipmentByClickAndCollectInterface
 *
 * @api
 */
interface IsCreatingShipmentByClickAndCollectInterface
{
    /**
     * Set is creating shipment by click and collect
     *
     * @param bool $isCreatingShipmentByClickAndCollect
     * @return void
     */
    public function setIsCreatingShipmentByClickAndCollect(bool $isCreatingShipmentByClickAndCollect): void;

    /**
     * Get is creating shipment by click and collect
     *
     * @return bool
     * @SuppressWarnings(PHPMD.BooleanGetMethodName)
     */
    public function getIsCreatingShipmentByClickAndCollect(): bool;
}
