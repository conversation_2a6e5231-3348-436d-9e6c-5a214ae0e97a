<?xml version="1.0"?>

<!--
  ~ Copyright © 2018 Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magestore_ClickAndCollectGraphQl::click_and_collect_api" sortOrder="50" title="Click And Collect Api">
                    <resource id="Magestore_ClickAndCollectAdminUi::click_and_collect" sortOrder="5" title="Access Click and collect" />
                </resource>
            </resource>
        </resources>
    </acl>
</config>