<?php

namespace Magestore\RewardpointsBugFix\Plugin\Controller\Adminhtml\Managepointbalances;

use Magestore\Rewardpoints\Controller\Adminhtml\Managepointbalances\DownloadSample;
use Magento\Framework\Component\ComponentRegistrar;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\LocalizedException;

class DownloadSamplePlugin extends DownloadSample
{

    /**
     * Plugin execute function to fix download
     *
     * @param DownloadSample $subject
     * @param callable $proceed
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Redirect
     * @throws \Exception
     */
    public function aroundExecute(DownloadSample $subject, callable $proceed)
    {
        try {
            $fileName = 'import_point_balance_sample.csv';
            $moduleDir = $this->componentRegistrar->getPath(ComponentRegistrar::MODULE, self::SAMPLE_FILES_MODULE);
            $fileAbsolutePath = $moduleDir . '/Files/Sample/' . $fileName;
            // phpcs:ignore Magento2.Functions.DiscouragedFunction
            if (!file_exists($fileAbsolutePath)) {
                $this->messageManager->addError(__('There is no sample file for this entity.'));
                return $this->resultRedirectFactory->create()->setPath('*/import');
            }
            // phpcs:ignore Magento2.Functions.DiscouragedFunction
            $fileContents = file_get_contents($fileAbsolutePath);
            if ($fileContents === false) {
                throw new LocalizedException(__('Error reading the sample file.'));
            }

            // phpcs:ignore Magento2.Functions.DiscouragedFunction
            $fileSize = filesize($fileAbsolutePath);

            return $this->fileFactory->create(
                $fileName,
                $fileContents,
                DirectoryList::VAR_DIR,
                'text/csv',
                $fileSize
            );

        } catch (\Exception $e) {
            $this->messageManager->addError(__('An error occurred: %1', $e->getMessage()));
            return $this->resultRedirectFactory->create()->setPath('*/import');
        }
    }
}
