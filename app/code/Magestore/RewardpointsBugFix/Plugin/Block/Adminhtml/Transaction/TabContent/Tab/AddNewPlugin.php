<?php

namespace Magestore\RewardpointsBugFix\Plugin\Block\Adminhtml\Transaction\TabContent\Tab;

use Magestore\Rewardpoints\Block\Adminhtml\Transaction\TabContent\Tab\AddNew;

class AddNewPlugin extends AddNew
{

    /**
     * Plugin fix getForm function
     *
     * @param AddNew $subject
     * @param callable $proceed
     * @return \Magento\Framework\Data\Form
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function aroundGetForm(AddNew $subject, callable $proceed)
    {
        if ($subject->_form == null) {
            return $subject->_formFactory->create();
        } else {
            return $proceed();
        }
    }
}
