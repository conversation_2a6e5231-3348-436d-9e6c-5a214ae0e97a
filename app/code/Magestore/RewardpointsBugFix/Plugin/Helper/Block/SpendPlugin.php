<?php

namespace Magestore\RewardpointsBugFix\Plugin\Helper\Block;

use Magento\Backend\App\Area\FrontNameResolver;
use Magestore\Rewardpoints\Helper\Block\Spend;

class SpendPlugin extends Spend
{

    /**
     * Plugin fix getQuote function
     *
     * @param Spend $subject
     * @param callable $proceed
     * @return mixed
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function aroundGetQuote(Spend $subject, callable $proceed)
    {
        if ($subject->_appState->getAreaCode() == FrontNameResolver::AREA_CODE) {
            return $subject->_quoteSessionBackendFactory->create()->getQuote();
        }
        $quoteId = $subject->_checkoutSessionFactory->create()->getQuoteId();
        if (!$quoteId) {
            return false;
        }
        return $proceed();
    }

    /**
     * Plugin fix getSliderData function
     *
     * @param Spend $subject
     * @param callable $proceed
     * @return \Magento\Framework\DataObject
     */
    public function aroundGetSliderData(Spend $subject, callable $proceed)
    {
        $quote = $subject->getQuote();
        if ($quote) {
            return $proceed();
        }
        return new \Magento\Framework\DataObject([]);
    }
}
