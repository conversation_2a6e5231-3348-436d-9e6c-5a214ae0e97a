<?xml version="1.0"?>
<!--
/**
 * Copyright © Magestore, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\GraphQl\Query\Resolver\Argument\FieldEntityAttributesPool">
        <arguments>
            <argument name="attributesInstances" xsi:type="array">
                <item name="msGetRewardPointTransactionHistory" xsi:type="object">Magestore\RewardpointsGraphQl\Model\Transaction\TransactionFilterAttributesForAst</item>
            </argument>
        </arguments>
    </type>
</config>
