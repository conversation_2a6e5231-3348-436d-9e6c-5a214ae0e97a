<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="sales_quote_address_collect_totals_before">
        <observer name="rewardpoints_graphql_sales_quote_address_collect_totals_before" instance="Magestore\Rewardpoints\Model\Total\Quote\ResetRewardpoints" />
    </event>
</config>
