<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\StoreGraphQl\Model\Resolver\Store\StoreConfigDataProvider">
        <arguments>
            <argument name="extendedConfigData" xsi:type="array">
                <item name="magestore_rewardpoints_general_enable" xsi:type="string">rewardpoints/general/enable</item>
                <item name="magestore_rewardpoints_general_point_name" xsi:type="string">rewardpoints/general/point_name</item>
                <item name="magestore_rewardpoints_general_point_names" xsi:type="string">rewardpoints/general/point_names</item>
                <item name="magestore_rewardpoints_general_point_image" xsi:type="string">rewardpoints/general/point_image</item>
                <item name="magestore_rewardpoints_general_show_policy_menu" xsi:type="string">rewardpoints/general/show_policy_menu</item>
                <item name="magestore_rewardpoints_general_policy_page" xsi:type="string">rewardpoints/general/policy_page</item>
                <item name="magestore_rewardpoints_general_show_welcome_page" xsi:type="string">rewardpoints/general/show_welcome_page</item>
                <item name="magestore_rewardpoints_general_welcome_page" xsi:type="string">rewardpoints/general/welcome_page</item>
                <item name="magestore_rewardpoints_earning_rounding_method" xsi:type="string">rewardpoints/earning/rounding_method</item>
                <item name="magestore_rewardpoints_earning_expire" xsi:type="string">rewardpoints/earning/invitation_order</item>
                <item name="magestore_rewardpoints_earning_max_balance" xsi:type="string">rewardpoints/earning/max_balance</item>
                <item name="magestore_rewardpoints_earning_by_tax" xsi:type="string">rewardpoints/earning/by_tax</item>
                <item name="magestore_rewardpoints_earning_by_shipping" xsi:type="string">rewardpoints/earning/by_shipping</item>
                <item name="magestore_rewardpoints_earning_earn_when_spend" xsi:type="string">rewardpoints/earning/earn_when_spend</item>
                <item name="magestore_rewardpoints_earning_order_invoice" xsi:type="string">rewardpoints/earning/order_invoice</item>
                <item name="magestore_rewardpoints_earning_holding_days" xsi:type="string">rewardpoints/earning/holding_days</item>
                <item name="magestore_rewardpoints_spending_redeemable_points" xsi:type="string">rewardpoints/spending/redeemable_points</item>
                <item name="magestore_rewardpoints_spending_max_points_per_order" xsi:type="string">rewardpoints/spending/max_points_per_order</item>
                <item name="magestore_rewardpoints_spending_max_point_default" xsi:type="string">rewardpoints/spending/max_point_default</item>
                <item name="magestore_rewardpoints_spending_spend_for_shipping" xsi:type="string">rewardpoints/spending/spend_for_shipping</item>
                <item name="magestore_rewardpoints_display_toplink" xsi:type="string">rewardpoints/display/toplink</item>
                <item name="magestore_rewardpoints_display_product" xsi:type="string">rewardpoints/display/product</item>
                <item name="magestore_rewardpoints_display_minicart" xsi:type="string">rewardpoints/display/minicart</item>
                <item name="magestore_rewardpoints_email_enable" xsi:type="string">rewardpoints/email/enable</item>
                <item name="magestore_rewardpoints_email_sender" xsi:type="string">rewardpoints/email/sender</item>
                <item name="magestore_rewardpoints_email_update_balance" xsi:type="string">rewardpoints/email/update_balance</item>
                <item name="magestore_rewardpoints_email_before_expire_transaction" xsi:type="string">rewardpoints/email/before_expire_transaction</item>
                <item name="magestore_rewardpoints_email_before_expire_days" xsi:type="string">rewardpoints/email/before_expire_days</item>
            </argument>
        </arguments>
    </type>
</config>
