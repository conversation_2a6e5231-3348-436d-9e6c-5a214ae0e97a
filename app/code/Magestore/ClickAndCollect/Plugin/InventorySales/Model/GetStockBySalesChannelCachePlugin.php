<?php
/**
 * Copyright © Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Magestore\ClickAndCollect\Plugin\InventorySales\Model;

use Magento\InventorySales\Model\GetStockBySalesChannel;
use Magento\InventorySales\Model\GetStockBySalesChannelCache;
use Magento\InventorySalesApi\Api\Data\SalesChannelInterface;
use Magestore\ClickAndCollectApi\Api\DataProvider\IsCreatingShipmentByClickAndCollectInterface;

/**
 * ClickAndCollect - GetStockBySalesChannelCachePlugin
 */
class GetStockBySalesChannelCachePlugin
{
    /**
     * @var IsCreatingShipmentByClickAndCollectInterface
     */
    protected $isCreatingShipmentByClickAndCollect;

    /**
     * @var GetStockBySalesChannel
     */
    protected $getStockBySalesChannel;

    /**
     * @param IsCreatingShipmentByClickAndCollectInterface $isCreatingShipmentByClickAndCollect
     * @param GetStockBySalesChannel $getStockBySalesChannel
     */
    public function __construct(
        IsCreatingShipmentByClickAndCollectInterface $isCreatingShipmentByClickAndCollect,
        GetStockBySalesChannel $getStockBySalesChannel
    ) {
        $this->isCreatingShipmentByClickAndCollect = $isCreatingShipmentByClickAndCollect;
        $this->getStockBySalesChannel = $getStockBySalesChannel;
    }

    /**
     * Around Execute
     *
     * @param GetStockBySalesChannelCache $subject
     * @param \Closure $proceed
     * @param SalesChannelInterface $salesChannel
     * @return \Magento\InventoryApi\Api\Data\StockInterface|mixed
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function aroundExecute(
        GetStockBySalesChannelCache $subject, // phpstan:ignore
        \Closure $proceed,
        SalesChannelInterface $salesChannel
    ) {
        if ($this->isCreatingShipmentByClickAndCollect->getIsCreatingShipmentByClickAndCollect()) {
            return $this->getStockBySalesChannel->execute($salesChannel);
        }
        return $proceed($salesChannel);
    }
}
