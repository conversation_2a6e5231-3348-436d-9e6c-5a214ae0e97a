<?php

/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @developer   <PERSON>
 * @category    Magestore
 * @package     Magestore_WebposAdyenTerminal
 * @copyright   Copyright (c) 2017 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 *
 */

namespace Magestore\WebposAdyenTerminal\Model\Source;

use Magento\Framework\Data\OptionSourceInterface;

/**
 * Class ServerType
 *
 * Option Source
 */
class ServerType implements OptionSourceInterface
{

    const SANDBOX = 0;
    const PRODUCTION = 1;

    /**
     * To Option Array
     *
     * @return array
     */
    public function toOptionArray()
    {
        return [
            ['label' => __('Sandbox'), 'value' => self::SANDBOX],
            ['label' => __('Live'), 'value' => self::PRODUCTION],
        ];
    }

    /**
     * Return Option Array
     *
     * @return array
     */
    public function getOptionArray()
    {
        return [self::SANDBOX => __('Sandbox'), self::PRODUCTION => __('Live')];
    }
}
