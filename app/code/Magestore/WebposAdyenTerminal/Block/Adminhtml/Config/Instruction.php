<?php

/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @developer   <PERSON> Coding
 * @category    Magestore
 * @package     Magestore_WebposAdyenTerminal
 * @copyright   Copyright (c) 2017 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 *
 */

namespace Magestore\WebposAdyenTerminal\Block\Adminhtml\Config;

/**
 * Class Instruction
 *
 * Instruction setup guide
 */
class Instruction extends \Magento\Config\Block\System\Config\Form\Fieldset
{
    /**
     * Prepare Layout
     *
     * @return \Magento\Config\Block\System\Config\Form\Fieldset
     */
    public function _prepareLayout()
    {
        $this->addChild(
            'webposadyenterminal_setup_guide',
            Setupguide::class
        );

        return parent::_prepareLayout();
    }

    /**
     * Render Guide
     *
     * @param \Magento\Framework\Data\Form\Element\AbstractElement $element
     * @return string
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function render(\Magento\Framework\Data\Form\Element\AbstractElement $element)
    {
        return $this->getChildHtml('webposadyenterminal_setup_guide');
    }
}
