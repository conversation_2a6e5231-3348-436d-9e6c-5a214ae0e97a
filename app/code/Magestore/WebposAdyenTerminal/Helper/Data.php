<?php

/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @developer   Louis Coding
 * @category    Magestore
 * @package     Magestore_WebposAdyenTerminal
 * @copyright   Copyright (c) 2017 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 *
 */

namespace Magestore\WebposAdyenTerminal\Helper;

/**
 * Class Data
 *
 * Get Data from config
 */
class Data extends \Magestore\Webpos\Helper\Payment
{
    /**
     * Payment method code
     */
    const CODE = 'adyen_terminal';

    /**
     * Get Payment Config
     *
     * @param array $configItems
     * @return array
     */
    public function getPaymentConfig($configItems = [])
    {
        $code = self::CODE;
        $configData = [];
        foreach ($configItems as $configItem) {
            $configData[$configItem] = $this->getStoreConfig("webpos/payment/$code/$configItem");
        }
        return $configData;
    }

    /**
     * Check Payment enable
     *
     * @return bool
     */
    public function isEnableAdyen()
    {
        $code = self::CODE;
        $enable = $this->getStoreConfig("webpos/payment/$code/active");
        return ($enable) ? true : false;
    }

    /**
     * Check Payment Is Default
     *
     * @return int
     */
    public function isDefault()
    {
        return (self::CODE == $this->getDefaultPaymentMethod()) ?
            \Magestore\Webpos\Api\Data\Payment\PaymentInterface::YES :
            \Magestore\Webpos\Api\Data\Payment\PaymentInterface::NO;
    }
}
