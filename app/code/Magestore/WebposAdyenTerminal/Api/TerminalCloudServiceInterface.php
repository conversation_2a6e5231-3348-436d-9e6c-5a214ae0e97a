<?php

/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @developer   <PERSON>
 * @category    Magestore
 * @package     Magestore_WebposAdyenTerminal
 * @copyright   Copyright (c) 2017 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 *
 */

namespace Magestore\WebposAdyenTerminal\Api;

/**
 * Interface TerminalCloudServiceInterface
 */
interface TerminalCloudServiceInterface
{
    /**
     * Check Adyen Terminal Enable
     *
     * @return bool
     */
    public function isEnable();

    /**
     * Check Adyen Terminal Configuration
     *
     * @return string
     */
    public function getConfigurationError();

    /**
     * Get default POIID
     *
     * @return string
     */
    public function getPOIID();

    /**
     * Get Connected Terminals
     *
     * @param string $poiid
     * @return string
     */
    public function getConnectedTerminal($poiid);

    /**
     * Trigger sync call sendSaleRequest on terminal
     *
     * @param mixed $payload
     * @return false|string
     * @throws \Adyen\AdyenException
     */
    public function sendSaleRequest($payload);

    /**
     * Trigger sync call sendRefundRequest on terminal
     *
     * @param mixed $payload
     * @return false|string
     * @throws \Adyen\AdyenException
     */
    public function sendRefundRequest($payload);
}
