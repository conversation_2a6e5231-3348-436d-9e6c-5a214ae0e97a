<?xml version="1.0" encoding="UTF-8"?>

<!--
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @developer   <PERSON> Coding
 * @category    Magestore
 * @package     Magestore_WebposAdyenTerminal
 * @copyright   Copyright (c) 2017 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 *
 */
  -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="webpos">
            <group id="payment">
                <group id="adyen_terminal" translate="label" sortOrder="56" type="text" showInDefault="1" showInWebsite="0"
                       showInStore="0">
                    <label>Adyen Terminal</label>
                    <field id="active" translate="label" sortOrder="1" type="select" showInDefault="1" showInWebsite="0"
                           showInStore="0">
                        <label>Enabled</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                    <field id="title" translate="label" sortOrder="5" type="text" showInDefault="1" showInWebsite="0"
                           showInStore="0">
                        <label>Title</label>
                        <validate>required-entry</validate>
                        <depends>
                            <field id="*/*/*/active">1</field>
                        </depends>
                    </field>
                    <field id="sort_order" translate="label" sortOrder="10" type="text" showInDefault="1"
                           showInWebsite="0" showInStore="0">
                        <label>Sort Order</label>
                        <depends>
                            <field id="*/*/*/active">1</field>
                        </depends>
                    </field>
                    <field id="poiid" translate="label" sortOrder="15" type="text" showInDefault="1"
                           showInWebsite="0" showInStore="0">
                        <label>POIID</label>
                        <validate>required-entry</validate>
                        <comment><![CDATA[[device model]-[serial number]. For example, V400m-*********]]></comment>
                        <depends>
                            <field id="*/*/*/active">1</field>
                        </depends>
                    </field>
                    <field id="merchant_account" translate="label" sortOrder="22" type="password" showInDefault="1"
                           showInWebsite="0" showInStore="0">
                        <label>Merchant Account for Cloud API</label>
                        <validate>required-entry</validate>
                        <depends>
                            <field id="*/*/*/active">1</field>
                        </depends>
                        <tooltip><![CDATA[The merchant account identifier you want to process the (transaction) request with. Find this at the top of the screen in the Adyen Customer Area, where you will see [YourCompanyAccount] > [YourMerchantAccount] . Please note that the merchant account is different from the company account; a company account can have one or more merchant accounts.]]></tooltip>
                        <comment><![CDATA[<a target="_blank" href="https://docs.adyen.com/get-started-with-adyen">Click here for explanation.</a>]]></comment>
                    </field>
                    <field id="api_key" translate="label" sortOrder="24" type="password" showInDefault="1"
                           showInWebsite="0" showInStore="0">
                        <label>API Key</label>
                        <validate>required-entry</validate>
                        <depends>
                            <field id="*/*/*/active">1</field>
                        </depends>
                        <tooltip><![CDATA[If you are unsure of your API Key, please follow these steps to get a new one: <br/>1. Log in to your Customer Area. <br/>2. Go to <strong>Developers > API credentials</strong>, and select the API credential username for your integration, for example, <strong>ws@Company.[YourCompanyAccount].</strong> <br/>3. Under Server <strong>settings > Authentication</strong>, select the <strong>API key</strong> tab. <br/>4. Select "Generate API key", then click the copy icon to store your API key securely. Finally, click "Save changes".]]></tooltip>
                        <comment><![CDATA[<a target="_blank" href="https://docs.adyen.com/user-management/how-to-get-the-api-key">Click here for explanation.</a>]]></comment>
                    </field>
                    <field id="server_type" translate="label" sortOrder="26" type="select" showInDefault="1"
                           showInWebsite="0" showInStore="0">
                        <label>API Server</label>
                        <validate>required-entry</validate>
                        <source_model>Magestore\WebposAdyenTerminal\Model\Source\ServerType</source_model>
                        <depends>
                            <field id="*/*/*/active">1</field>
                        </depends>
                        <tooltip><![CDATA[ In the test mode you must use test cards. See section Documentation & Support for the link to the test cards]]></tooltip>
                    </field>
                    <field id="live_prefix" translate="label" sortOrder="28" type="text" showInDefault="1"
                           showInWebsite="0" showInStore="0">
                        <label>Live URL prefix</label>
                        <validate>required-entry</validate>
                        <depends>
                            <field id="*/*/*/active">1</field>
                            <field id="*/*/*/server_type">LIVE</field>
                        </depends>
                        <tooltip><![CDATA[e.g. if your live endpoint is: <br/> <i>https://1234a567bcd89ef0-MagentoCompany-checkout-live.adyenpayments.com</i> <br/> please type:  <strong>1234a567bcd89ef0-MagentoCompany</strong> in this field.]]></tooltip>
                        <comment><![CDATA[Provide the unique live url prefix: <strong>[random]-[company name]</strong> from the "API URLs and Response" menu in the Adyen Customer Area. For more information, please check <a href="https://docs.adyen.com/developers/development-resources/live-endpoints#checkoutendpoints">  our documentation</a>.]]></comment>
                    </field>
                    <group id="guides" translate="label" sortOrder="30" type="text" showInDefault="1" showInWebsite="0">
                        <label>Guide</label>
                        <field id="setup_guide" translate="label" sortOrder="6" type="text" showInDefault="1" showInWebsite="0">
                            <label>Setup guide</label>
                            <frontend_model>Magestore\WebposAdyenTerminal\Block\Adminhtml\Config\Instruction</frontend_model>
                        </field>
                    </group>
                </group>
            </group>
        </section>
    </system>
</config>
