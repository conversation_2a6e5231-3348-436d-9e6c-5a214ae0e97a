<?xml version="1.0" encoding="UTF-8"?>

<!--
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @developer   <PERSON> Coding
 * @category    Magestore
 * @package     Magestore_WebposAdyenTerminal
 * @copyright   Copyright (c) 2017 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 *
 */
  -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magestore\WebposAdyenTerminal\Api\TerminalCloudServiceInterface"
                type="Magestore\WebposAdyenTerminal\Model\TerminalCloudService"/>
    <type name="Magestore\Payment\Model\Payment\Type\TerminalPayments">
        <arguments>
            <argument name="data" xsi:type="array">
                <item name="adyen_terminal" xsi:type="string">adyen_terminal</item>
            </argument>
        </arguments>
    </type>
    <type name="Magestore\Webpos\Model\Config\ConfigRepository">
        <plugin name="WebposAdyenTerminalGetConfigPathPlugin"
                type="Magestore\WebposAdyenTerminal\Plugin\Webpos\ConfigRepositoryPlugin" sortOrder="1" disabled="false" />
    </type>
    <type name="Magestore\Payment\Model\Payment\RefundType\AcceptedPayments">
        <arguments>
            <argument name="data" xsi:type="array">
                <item name="adyen_terminal" xsi:type="string">adyen_terminal</item>
            </argument>
        </arguments>
    </type>
    <type name="Magestore\Payment\Model\Payment\RefundType\UseTransactionPayments">
        <arguments>
            <argument name="data" xsi:type="array">
                <item name="adyen_terminal" xsi:type="string">adyen_terminal</item>
            </argument>
        </arguments>
    </type>
</config>