<?xml version="1.0" encoding="UTF-8"?>

<!--
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @developer   <PERSON> Coding
 * @category    Magestore
 * @package     Magestore_WebposAdyenTerminal
 * @copyright   Copyright (c) 2017 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 *
 */
  -->

<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <route url="/V1/webpos/adyenterminal/getConnectedTerminal" method="POST">
        <service class="Magestore\WebposAdyenTerminal\Api\TerminalCloudServiceInterface" method="getConnectedTerminal"/>
        <resources>
            <resource ref="Magestore_Webpos::manage_pos" />
        </resources>
    </route>
    <route url="/V1/webpos/adyenterminal/sendSaleRequest" method="POST">
        <service class="Magestore\WebposAdyenTerminal\Api\TerminalCloudServiceInterface" method="sendSaleRequest"/>
        <resources>
            <resource ref="Magestore_Webpos::manage_pos" />
        </resources>
    </route>
    <route url="/V1/webpos/adyenterminal/sendRefundRequest" method="POST">
        <service class="Magestore\WebposAdyenTerminal\Api\TerminalCloudServiceInterface" method="sendRefundRequest"/>
        <resources>
            <resource ref="Magestore_Webpos::manage_pos" />
        </resources>
    </route>
</routes>
