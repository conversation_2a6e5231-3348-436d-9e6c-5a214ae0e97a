<?php
/**
 * @var \Magestore\WebposAdyenTerminal\Block\Adminhtml\Config\Setupguide $block
 */
?>
<tr>
    <td class="label">
        <label>
            <span><?= $block->escapeHtml(__('Installation guide'))?></span>
        </label>
    </td>
    <td class="value adyen-terminal-installation-guide">
        <h4 class="title"><?= $block->escapeHtml(__('Install the Adyen APIs Library'))?></h4>
        <ul class="adyen-terminal-installation-skd-guide">
            <li>
                <?= $block->escapeHtml(__('If you can access to your server via SSH:'));?><br />
                <div class="guide-content">
                    <?= $block->escapeHtml(('Use cd command to your Magento root folder. 
                    After that, enter the command:'));?>
                    <br />
                    <b>composer require "adyen/php-api-library"</b>
                    <br />
                    <a href="https://github.com/Adyen/adyen-php-api-library">
                        <?= $block->escapeHtml(__('View detail'))?>
                    </a>
                </div>
            </li>
        </ul>
        <h4 class="title"><?= $block->escapeHtml(('Test the Terminal connection'))?></h4>
        <ul class="adyen-terminal-installation-test">
            <li><?= $block->escapeHtml(('After install the Adyen APIs Library and save all the API information,
             you can click the button below to test the connection'));?></li>
            <li>
                <button type="button" id="webpos-adyen-terminal-integration-test-api">
                    <?= $block->escapeHtml(__('Test Terminal connection'));?>
                </button>
            </li>
        </ul>
        <div id="webpos-adyen-terminal-integration-test-response">
            <div class="loader hide"></div>
            <div class="success hide"><?= $block->escapeHtml(__('Success'));?></div>
            <div class="error hide"><?= $block->escapeHtml(__('Error'));?></div>
        </div>
    </td>
    <td class=""></td>
</tr>
<script>
    require([
        'jquery'
    ], function ($) {
        var testAPIUrl = '<?= $block->escapeUrl($block->getTestApiUrl());?>';
        $(document).ready(function(){
            $('#webpos-adyen-terminal-integration-test-api').click(function(){
                $("#webpos-adyen-terminal-integration-test-response .loader").removeClass('hide');
                $.ajax({
                    method:'get',
                    url: testAPIUrl,
                    success: function (result) {
                        $("#webpos-adyen-terminal-integration-test-response .loader").addClass('hide');
                        var response = {};
                        if (result && result.responseText != 'undefined' && result.responseText) {
                            response = JSON.parse(result.responseText);
                        } else {
                            response = result;
                        }
                        if (response && response.success) {
                            $("#webpos-adyen-terminal-integration-test-response .success").removeClass('hide');
                            $("#webpos-adyen-terminal-integration-test-response .error").addClass('hide');
                        } else {
                            if (response && response.message) {
                                alert(response.message);
                            }
                            $("#webpos-adyen-terminal-integration-test-response .success").addClass('hide');
                            $("#webpos-adyen-terminal-integration-test-response .error").removeClass('hide');
                        }
                    },
                    error: function () {
                        $("#webpos-adyen-terminal-integration-test-response .loader").addClass('hide');
                        alert('<?= $block->escapeHtml(__('Please check your network connection'));?>');
                    }
                });
            });
        });
    });
</script>