"Please check your network connection","Please check your network connection"
"Test Terminal connection","Test Terminal connection"
"After install the Adyen APIs Library and save all the API information, you can click the button below to test the connection","After install the Adyen APIs Library and save all the API information, you can click the button below to test the connection"
"Test the Terminal connection","Test the Terminal connection"
"View detail","View detail"
"Use cd command to your Magento root folder. After that, enter the command:","Use cd command to your Magento root folder. After that, enter the command:"
"If you can access to your server via SSH:","If you can access to your server via SSH:"
"Install the Adyen APIs Library","Install the Adyen APIs Library"
"Installation guide","Installation guide"
"If you are unsure of your API Key, please follow these steps to get a new one: 1. Log in to your Customer Area. 2. Go to <strong>Developers > API credentials</strong>, and select the API credential username for your integration, for example, <strong>ws@Company.[YourCompanyAccount].</strong> 3. Under Server settings > Authentication, select the API key tab. 4. Select "Generate API key", then click the copy icon to store your API key securely. Finally, click "Save changes".", "If you are unsure of your API Key, please follow these steps to get a new one: 1. Log in to your Customer Area. 2. Go to <strong>Developers > API credentials</strong>, and select the API credential username for your integration, for example, <strong>ws@Company.[YourCompanyAccount].</strong> 3. Under Server settings > Authentication, select the API key tab. 4. Select "Generate API key", then click the copy icon to store your API key securely. Finally, click "Save changes"."
"API key", "API key"
