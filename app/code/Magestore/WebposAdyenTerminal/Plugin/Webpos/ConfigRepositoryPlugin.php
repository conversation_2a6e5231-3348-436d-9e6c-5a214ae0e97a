<?php

/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @developer   <PERSON> Coding
 * @category    Magestore
 * @package     Magestore_WebposAdyenTerminal
 * @copyright   Copyright (c) 2017 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 *
 */

namespace Magestore\WebposAdyenTerminal\Plugin\Webpos;

use Magestore\Webpos\Model\Config\ConfigRepository;

/**
 * Class ConfigRepositoryPlugin
 *
 * Plugin ConfigRepository
 */
class ConfigRepositoryPlugin
{
    /**
     * Added configuration of adyen terminal to POS
     *
     * @param ConfigRepository $subject
     * @param array $result
     * @return array
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterGetConfigPath(ConfigRepository $subject, $result)
    {
        $result [] = 'webpos/payment/adyen_terminal/active';
        $result [] = 'webpos/payment/adyen_terminal/poiid';

        return $result;
    }
}
