<?php

/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @developer   <PERSON> Coding
 * @category    Magestore
 * @package     Magestore_WebposAdyenTerminal
 * @copyright   Copyright (c) 2017 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 *
 */

namespace Magestore\WebposAdyenTerminal\Controller\Adminhtml\Api;

/**
 * Class Test
 *
 * Test terminal API
 * @SuppressWarnings(PHPMD.AllPurposeAction)
 */
class Test extends \Magento\Backend\App\Action
{
    /**
     * @var \Magestore\WebposAdyenTerminal\Api\TerminalCloudServiceInterface
     */
    protected $terminalCloudService;

    /**
     * @var \Magento\Framework\Controller\ResultFactory
     */
    protected $resultFactory;

    /**
     * Test constructor.
     *
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magestore\WebposAdyenTerminal\Api\TerminalCloudServiceInterface $terminalCloudService
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magestore\WebposAdyenTerminal\Api\TerminalCloudServiceInterface $terminalCloudService
    ) {
        parent::__construct($context);
        $this->terminalCloudService = $terminalCloudService;
        $this->resultFactory = $context->getResultFactory();
    }

    /**
     * @return \Magento\Framework\Controller\Result\Json $resultJson
     */
    public function execute()
    {
        $response = [
            'url' => '',
            'message' => '',
            'success' => true
        ];
        if ($message = $this->terminalCloudService->getConfigurationError()) {
            $response['success'] = false;
            $response['message'] = __($message);
        } else {
            $poiid = $this->terminalCloudService->getPOIID();
            $data = json_decode($this->terminalCloudService->getConnectedTerminal($poiid), true);
            $response['success'] = (isset($data['status']) && $data['status']) ? true : false;
            $response['message'] = (isset($data['message']) && $data['message']) ? $data['message']
                : __('Connection failed. Please contact admin to check the configuration of Adyen APIs.');
        }
        $resultJson = $this->resultFactory->create(\Magento\Framework\Controller\ResultFactory::TYPE_JSON);
        return $resultJson->setData($response);
    }
}
