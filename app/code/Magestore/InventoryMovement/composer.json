{"name": "magestore/module-inventory-movement", "description": "N/A", "require": {"php": "~7.1.3||~7.2.0||~7.3.0||~7.4.0", "magento/framework": "*", "magento/module-inventory": "*", "magento/module-inventory-api": "*", "magento/module-inventory-sales-api": "*", "magento/module-inventory-sales": "*", "magento/module-catalog-inventory": "*", "magento/module-inventory-indexer": "*", "magestore/module-core": "*"}, "type": "magento2-module", "license": ["OSL-3.0", "AFL-3.0"], "version": "1.0.0", "authors": [{"name": "magestore", "email": "<EMAIL>", "homepage": "https://www.magestore.com/", "role": "Developer"}], "autoload": {"files": ["registration.php"], "psr-4": {"Magestore\\InventoryMovement\\": ""}}}