<?xml version="1.0"?>
<!--
/**
 * Copyright © Magestore, Inc. All rights reserved.
 * See COPYING.txt for license details.
   KEH fix - disable crons that locks DB.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Cron:etc/crontab.xsd">
    <group id="default">
        <job name="aggregate_orders_potential_error_data"
             instance="Magestore\InventoryMovement\Cron\AggregateOrdersPotentialErrorData" method="execute">
            <schedule>10 3 31 2 *</schedule>
        </job>
    </group>
</config>
