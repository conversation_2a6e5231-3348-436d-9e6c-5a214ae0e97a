<?xml version="1.0"?>
<!--
  ~ Copyright © Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="webpos">
            <group id="payment">
                <group id="dojo_integration" translate="label comment" sortOrder="100" type="text" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Dojo Terminal</label>
                    <!--<comment>Terminal Supported: Dojo</comment>-->
                    <field id="enable" translate="label" sortOrder="10" type="select" showInDefault="1" showInWebsite="0" showInStore="0">
                        <label>Enabled</label>
                        <validate>required-entry</validate>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                    <field id="title" translate="label" sortOrder="20" type="text" showInDefault="1" showInWebsite="0" showInStore="0">
                        <label>Title</label>
                        <validate>required-entry</validate>
                        <depends>
                            <field id="*/*/*/enable">1</field>
                        </depends>
                    </field>
                    <field id="is_sandbox" translate="label" sortOrder="10" type="select" showInDefault="1" showInWebsite="0">
                        <label>Sandbox Mode</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                        <validate>required-entry</validate>
                        <depends>
                            <field id="*/*/*/enable">1</field>
                        </depends>
                    </field>
                    <field id="host_url" translate="label" sortOrder="20" type="text" showInDefault="1" showInWebsite="0">
                        <label>Host Url</label>
                        <validate>required-entry</validate>
                        <depends>
                            <field id="*/*/*/enable">1</field>
                            <field id="*/*/*/is_sandbox">0</field>
                        </depends>
                    </field>
                    <field id="host_url_sandbox" translate="label" sortOrder="20" type="text" showInDefault="1" showInWebsite="0">
                        <label>Host Url</label>
                        <validate>required-entry</validate>
                        <depends>
                            <field id="*/*/*/enable">1</field>
                            <field id="*/*/*/is_sandbox">1</field>
                        </depends>
                    </field>
                    <field id="installer_id" translate="label" sortOrder="90" type="text" showInDefault="1" showInWebsite="0">
                        <label>Installer-Id</label>
                        <validate>required-entry</validate>
                        <depends>
                            <field id="*/*/*/enable">1</field>
                        </depends>
                    </field>
                    <field id="account" translate="label" sortOrder="30" type="text" showInDefault="1" showInWebsite="0" showInStore="0">
                        <label>Account</label>
                        <validate>required-entry</validate>
                        <depends>
                            <field id="*/*/*/enable">1</field>
                        </depends>
                    </field>
                    <field id="password" translate="label" sortOrder="60" type="text" showInDefault="1" showInWebsite="0">
                        <label>Password</label>
                        <validate>required-entry</validate>
                        <depends>
                            <field id="*/*/*/enable">1</field>
                        </depends>
                    </field>
                    <field id="sort_order" translate="label" sortOrder="120" type="text" showInDefault="1" showInWebsite="0" showInStore="0">
                        <label>Sort Order</label>
                        <depends>
                            <field id="*/*/*/enable">1</field>
                        </depends>
                    </field>
                    <field id="test_configuration" translate="label" sortOrder="150" type="text" showInDefault="1" showInWebsite="0">
                        <frontend_model>Magestore\WebposDojoAdminUi\Block\Adminhtml\Config\Instruction</frontend_model>
                    </field>
                </group>
            </group>
        </section>
    </system>
</config>