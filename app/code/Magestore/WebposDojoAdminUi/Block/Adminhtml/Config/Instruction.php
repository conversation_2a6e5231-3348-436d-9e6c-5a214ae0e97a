<?php
/**
 * Copyright © Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Magestore\WebposDojoAdminUi\Block\Adminhtml\Config;

use Magento\Config\Block\System\Config\Form\Fieldset;
use Magento\Framework\Data\Form\Element\AbstractElement;

/**
 * Instruction Block
 */
class Instruction extends Fieldset
{
    /**
     * Prepare layout
     *
     * @return Instruction
     */
    protected function _prepareLayout()
    {
        $this->addChild(
            'dojo_terminal_setup_guide',
            Setupguide::class
        );

        return parent::_prepareLayout();
    }

    /**
     * Render
     *
     * @param AbstractElement $element
     * @return string
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function render(AbstractElement $element)
    {
        return $this->getChildHtml('dojo_terminal_setup_guide');
    }
}
