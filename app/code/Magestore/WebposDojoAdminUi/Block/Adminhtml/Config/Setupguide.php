<?php
/**
 * Copyright © Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Magestore\WebposDojoAdminUi\Block\Adminhtml\Config;

use Magestore\WebposDojo\Helper\Data as DojoHelper;

/**
 * Display setup api configuration
 */
class Setupguide extends \Magento\Backend\Block\Template
{
    /**
     * @var string
     */
    protected $_template = 'Magestore_WebposDojoAdminUi::config/setupguide.phtml';

    public $testButtonId = 'webpos-dojo-terminal-test-api-btn';
    public $testResponseId = 'webpos-dojo-terminal-test-api-response';
    public $setupGuideClassName = 'dojo-terminal-installation-guide';

    /** @var DojoHelper */
    protected $dojoHelper;

    /**
     * Constructor
     *
     * @param \Magento\Backend\Block\Template\Context $context
     * @param DojoHelper $dojoHelper
     * @param array $data
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        DojoHelper $dojoHelper,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->dojoHelper = $dojoHelper;
    }

    /**
     * Get Test API URL
     *
     * @return string
     */
    public function getTestApiUrl()
    {
        $isSandbox = $this->dojoHelper->isSandbox();
        $isEnable = $this->dojoHelper->isEnableDojo();
        $apiUrl = '';
        if ($isEnable) {
            $apiUrl .= 'https://'.$this->dojoHelper->getAccount().'.';
            $apiUrl .= $isSandbox ? $this->dojoHelper->getHostUrlSandbox() : $this->dojoHelper->getHostUrl();
            $apiUrl .= '/pac/terminals'.'?status=AVAILABLE';
        }
        return $apiUrl;
    }

    /**
     * Get Authorization
     *
     * @return string
     */
    public function getAuthorization()
    {
        $account = $this->dojoHelper->getAccount();
        $password = $this->dojoHelper->getPassword();

        if ($account && $password) {
            return 'Basic '.base64_encode($account.":".$password);
        }
        return '';
    }

    /**
     * Get Accept Header
     *
     * @return string
     */
    public function getAcceptHeader()
    {
        return 'application/connect.v2+json';
    }

    /**
     * Get Software house id
     *
     * @return string
     */
    public function getSoftwareHouseId()
    {
        return $this->dojoHelper->getInstallerId();
    }

    /**
     * Get Installer Id
     *
     * @return string
     */
    public function getInstallerId()
    {
        return $this->dojoHelper->getInstallerId();
    }
}
