<?php
/**
 * @var Magestore\WebposDojoAdminUi\Block\Adminhtml\Config\Setupguide $block
 * @var \Magento\Framework\Escaper $escaper
 */
?>
<tr>
    <td class="label">
        <label>
            <span><?= $escaper->escapeHtml(__('Test API Configuration')) ?></span>
        </label>
    </td>
    <td class="value <?= $escaper->escapeHtml($block->setupGuideClassName); ?>">
        <ul class="stripe-installation-test">
            <li>
                <?= $escaper->escapeHtml(__(
                    'After saving all the API information, '
                    . 'you can click the button below to test the connection'
                )); ?>
            </li>
            <li>
                <button type="button"
                        id="<?= $escaper->escapeHtml($block->testButtonId); ?>">
                    <?= $escaper->escapeHtml(__('Test API connection')); ?>
                </button>
            </li>
            <li>
                <div id="<?= $escaper->escapeHtml($block->testResponseId); ?>">
                    <div class="loader hide"></div>
                    <div class="success hide"><?= $escaper->escapeHtml(__('The API Configuration is Success')); ?></div>
                    <div class="error hide"><?= $escaper->escapeHtml(__('The API Configuration is Error')); ?></div>
                </div>
            </li>
        </ul>
    </td>
    <td></td>
</tr>

<script>
    require([
        'jquery'
    ], function (jQuery) {
        var testAPIUrl = '<?= $escaper->escapeUrl($block->getTestApiUrl());?>';
        var headers = {
            'Content-Type': 'application/json',
            'Authorization': '<?= $escaper->escapeHtml($block->getAuthorization());?>',
            'Accept': '<?= $escaper->escapeHtml($block->getAcceptHeader());?>',
            'Software-House-Id': '<?= $escaper->escapeHtml($block->getSoftwareHouseId());?>',
            'Installer-Id': '<?= $escaper->escapeHtml($block->getInstallerId());?>',
        };
        jQuery(document).ready(function () {
            jQuery('#<?= $escaper->escapeHtml($block->testButtonId); ?>').click(function () {
                jQuery("#<?= $escaper->escapeHtml($block->testResponseId); ?> .loader").removeClass('hide');
                jQuery.ajax({
                    method: 'get',
                    url: testAPIUrl,
                    headers: headers,
                    mode: 'cors',
                    success: function (result) {
                        jQuery("#<?= $escaper->escapeHtml($block->testResponseId); ?> .loader").addClass('hide');
                        if (result && result.terminals && result.terminals.length > 0) {
                            jQuery("#<?= $escaper->escapeHtml($block->testResponseId); ?> .success")
                                .removeClass('hide');
                            jQuery("#<?= $escaper->escapeHtml($block->testResponseId); ?> .error").addClass('hide');
                        } else {
                            jQuery("#<?= $escaper->escapeHtml($block->testResponseId); ?> .success").addClass('hide');
                            jQuery("#<?= $escaper->escapeHtml($block->testResponseId); ?> .error").removeClass('hide');
                        }
                    },
                    error: function (e) {
                        if (e && e.responseText != 'undefined' && e.responseText) {
                            var response = JSON.parse(e.responseText);
                            jQuery("#<?= $escaper->escapeHtml($block->testResponseId); ?> .error")
                                .html(response.userMessage);
                        }
                        jQuery("#<?= $escaper->escapeHtml($block->testResponseId); ?> .loader").addClass('hide');
                        jQuery("#<?= $escaper->escapeHtml($block->testResponseId); ?> .success").addClass('hide');
                        jQuery("#<?= $escaper->escapeHtml($block->testResponseId); ?> .error").removeClass('hide');
                    }
                });
            });
        });
    });
</script>