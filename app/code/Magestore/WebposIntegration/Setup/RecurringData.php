<?php

namespace Magestore\WebposIntegration\Setup;

use Magento\Framework\Setup\InstallDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magestore\WebposIntegration\Api\ApiServiceInterface;

/**
 * Webpos - RecurringData
 */
class RecurringData implements InstallDataInterface
{
    /**
     * @var ApiServiceInterface
     */
    protected $apiService;

    /**
     * Constructor
     *
     * @param ApiServiceInterface $apiService
     */
    public function __construct(
        ApiServiceInterface $apiService
    ) {
        $this->apiService = $apiService;
    }

    /**
     * @inheritDoc
     */
    public function install(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $this->apiService->setupIntegration();
    }
}
