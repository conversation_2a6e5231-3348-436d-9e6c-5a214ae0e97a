<?xml version="1.0"?>
<integrations>
    <integration name="Magestore POS">
        <resources>
            <resource name="Magento_Backend::admin" />
            <resource name="Magestore_Webpos::posclient" />
            <resource name="Magestore_Webpos::manage_pos" />
            <resource name="Magestore_Webpos::manage_session" />
            <resource name="Magestore_Webpos::make_adjustment" />
            <resource name="Magestore_Webpos::check_external_stock" />
            <resource name="Magestore_Webpos::refund" />
            <resource name="Magestore_Webpos::manage_order" />
            <resource name="Magestore_Webpos::manage_order_created_at_location_of_staff" />
            <resource name="Magestore_Webpos::manage_order_created_at_all_location" />
            <resource name="Magestore_Webpos::manage_order_that_are_created_at_or_assigned_to_location_of_staff" />
            <resource name="Magestore_Webpos::manage_all_orders_in_system" />
            <resource name="Magestore_Webpos::manage_checkout" />
            <resource name="Magestore_Webpos::custom_price_on_item" />
        </resources>
    </integration>
</integrations>