<?xml version="1.0" encoding="UTF-8"?>

<!--
  ~ Copyright © 2018 Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Webapi\Controller\Rest\RequestProcessorPool">
        <arguments>
            <argument name="requestProcessors" xsi:type="array">
                <item name="pos_request_processor" xsi:type="object">Magestore\WebposIntegration\Controller\Rest\RequestProcessor</item>
            </argument>
        </arguments>
    </type>
</config>