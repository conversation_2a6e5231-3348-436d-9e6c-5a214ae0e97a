<?php
/**
 * Copyright © 2016 Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Magestore\DropshipSuccess\Block\Supplier\Form\Resetpassword $block */
?>
<form action="<?php /* @escapeNotVerified */ echo $block->getPostUrl(); ?>"
      method="post"
      id="form-validate"
      class="form password reset"
      data-mage-init='{"validation":{}}'>
    <fieldset class="fieldset" data-hasrequired="<?php /* @escapeNotVerified */ echo __('* Required Fields'); ?>">
        <div class="field password required" data-mage-init='{"passwordStrengthIndicator": {}}'>
            <label class="label" for="password"><span><?php /* @escapeNotVerified */ echo __('New Password'); ?></span></label>
            <div class="control">
                <input type="password" class="input-text" name="password" id="password"
                       data-password-min-length="<?php echo $block->escapeHtml($block->getMinimumPasswordLength()) ?>"
                       data-password-min-character-sets="<?php echo $block->escapeHtml($block->getRequiredCharacterClassesNumber()) ?>"
                       data-validate="{required:true, 'validate-customer-password':true}"
                       autocomplete="off">
                <div id="password-strength-meter-container" data-role="password-strength-meter">
                    <div id="password-strength-meter" class="password-strength-meter">
                        <?php /* @escapeNotVerified */ echo __('Password Strength'); ?>:
                        <span id="password-strength-meter-label" data-role="password-strength-meter-label">
                            <?php /* @escapeNotVerified */ echo __('No Password'); ?>
                        </span>
                    </div>
                </div>
            </div>
        </div>
        <div class="field confirmation required">
            <label class="label" for="password-confirmation"><span><?php /* @escapeNotVerified */ echo __('Confirm New Password'); ?></span></label>
            <div class="control">
                <input type="password" class="input-text" name="password_confirmation" id="password-confirmation" data-validate="{required:true,equalTo:'#password'}" autocomplete="off">
            </div>
        </div>
    </fieldset>
    <div class="actions-toolbar">
        <div class="primary">
            <button type="submit" class="action submit primary"><span><?php /* @escapeNotVerified */ echo __('Set a New Password'); ?></span></button>
        </div>
    </div>
</form>
