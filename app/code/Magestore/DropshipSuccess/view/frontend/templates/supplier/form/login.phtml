<?php
/**
 * Copyright © 2016 Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Magestore\DropshipSuccess\Block\Supplier\Form\Login $block */
?>
<?php
/**
 * Customer login form template
 *
 * @see \Magestore\DropshipSuccess\Block\Supplier\Form\Login
 * @var $block \Magestore\DropshipSuccess\Block\Supplier\Form\Login
 */
?>
<div class="block block-customer-login">
    <div class="block-content" aria-labelledby="block-customer-login-heading">
        <form class="form form-login"
              action="<?php /* @escapeNotVerified */ echo $block->getPostActionUrl() ?>"
              method="post"
              id="login-form"
              data-mage-init='{"validation":{}}'>
            <?php echo $block->getBlockHtml('formkey'); ?>
            <fieldset class="fieldset login" data-hasrequired="<?php /* @escapeNotVerified */ echo __('* Required Fields') ?>">
                <div class="field email required">
                    <label class="label" for="email"><span><?php /* @escapeNotVerified */ echo __('Email') ?></span></label>
                    <div class="control">
                        <input name="login[username]" id="email" type="text" class="input-text" title="<?php /* @escapeNotVerified */ echo __('Email') ?>" data-validate="{required:true}">
                    </div>
                </div>
                <div class="field password required">
                    <label for="pass" class="label"><span><?php /* @escapeNotVerified */ echo __('Password') ?></span></label>
                    <div class="control">
                        <input name="login[password]" type="password" class="input-text" id="pass" title="<?php /* @escapeNotVerified */ echo __('Password') ?>" data-validate="{required:true}">
                    </div>
                </div>
                <?php echo $block->getChildHtml('form_additional_info'); ?>
                <div class="actions-toolbar">
                    <div class="primary"><button type="submit" class="action login primary" name="send" id="send2"><span><?php /* @escapeNotVerified */ echo __('Sign In') ?></span></button></div>
                    <div class="secondary"><a class="action remind" href="<?php /* @escapeNotVerified */ echo $block->getForgotPasswordUrl() ?>"><span><?php /* @escapeNotVerified */ echo __('Forgot Your Password?') ?></span></a></div>
                </div>
            </fieldset>
        </form>
    </div>
</div>
