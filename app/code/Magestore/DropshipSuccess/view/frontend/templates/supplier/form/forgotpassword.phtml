<?php
/**
 * Copyright © 2016 Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<form class="form password forget"
      action="<?php /* @escapeNotVerified */ echo $block->getPostUrl() ?>"
      method="post"
      id="form-validate"
      data-mage-init='{"validation":{}}'>
    <fieldset class="fieldset" data-hasrequired="<?php /* @escapeNotVerified */ echo __('* Required Fields') ?>">
        <div class="field note"><?php /* @escapeNotVerified */ echo __('Please enter your email address below to receive a password reset link.'); ?></div>
        <div class="field email required">
            <label for="email_address" class="label"><span><?php /* @escapeNotVerified */ echo __('Email') ?></span></label>
            <div class="control">
                <input type="email" name="email" alt="email" id="email_address" class="input-text" data-validate="{required:true, 'validate-email':true}">
            </div>
        </div>
        <?php echo $block->getChildHtml('form_additional_info'); ?>
    </fieldset>
    <div class="actions-toolbar">
        <div class="primary">
            <button type="submit" class="action submit primary"><span><?php /* @escapeNotVerified */ echo __('Reset My Password') ?></span></button>
        </div>
        <div class="secondary">
            <a class="action back" href="<?php /* @escapeNotVerified */ echo $block->getLoginUrl() ?>"><span><?php /* @escapeNotVerified */ echo __('Go back') ?></span></a>
        </div>
    </div>
</form>
