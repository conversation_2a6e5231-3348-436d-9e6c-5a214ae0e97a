<?xml version="1.0"?>
<!--
  ~ Copyright © 2016 Magestore. All rights reserved.
  ~ See COPYING.txt for license details.
  -->

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="supplier_account"/>
    <body>
        <referenceBlock name="page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Supplier's Products</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="content">
            <block class="Magestore\DropshipSuccess\Block\Supplier\Product" name="supplier.product" cacheable="false" />
        </referenceContainer>
    </body>
</page>
