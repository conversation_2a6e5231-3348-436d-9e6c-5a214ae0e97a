<?php
/**
 * Copyright © 2016 Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Magestore\DropshipSuccess\Ui\DataProvider\DropshipRequest\DataForm\Modifier;

use Magento\Framework\App\ObjectManager;
use Magento\Ui\Component\Form;
use Magestore\DropshipSuccess\Block\Adminhtml\DropshipRequest\Edit\Tab\Info\OrderInformation as BlockOrderInformation;

/**
 * Data provider for Configurable panel
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class OrderInformation extends AbstractModifier
{
    /**
     * @var array
     */
    protected $loadedData;

    /**
     * @inheritDoc
     */
    public function modifyData(array $data)
    {
        $data = array_replace_recursive(
            $data,
            $this->getData()
        );
        return $data;
    }

    /**
     * @inheritDoc
     */
    public function getData()
    {
        if (!empty($this->loadedData)) {
            return $this->loadedData;
        }
        $this->loadedData = [];
//        $supplier = $this->locator->getSupplier();
//        if ($supplier && $supplier->getId()) {
//            $this->loadedData[$supplier->getId()] = $supplier->getData();
//        }
        return $this->loadedData;
    }

    /**
     * @inheritDoc
     */
    public function modifyMeta(array $meta)
    {
        $meta = array_replace_recursive(
            $meta,
            $this->getOrderInformation($meta)
        );
        return $meta;
    }

    /**
     * Get Order Information
     *
     * @param array $meta
     * @return array
     */
    public function getOrderInformation($meta)
    {
        $meta['order_information']['arguments']['data']['config'] = [
            'label' => __('Sales Information'),
            'collapsible' => true,
            'visible' => true,
            'opened' => true,
            'dataScope' => 'data',
            'componentType' => Form\Fieldset::NAME
        ];
        $meta['order_information']['children'] = $this->getOrderInformationChildren();
        return $meta;
    }

    /**
     * Get Order Information Children
     *
     * @return array
     */
    public function getOrderInformationChildren()
    {
        $children = [
            'information' => $this->getOrderInformationContainer()
        ];
        return $children;
    }

    /**
     * Get Order Information Container
     *
     * @return array
     */
    public function getOrderInformationContainer()
    {
        $container = [
            'arguments' => [
                'data' => [
                    'config' => [
                        'componentType' => 'container',
                        'visible' => true,
                        'dataType' => 'container'
                    ]
                ]
            ],
            'children' => [
                'html_content' => [
                    'arguments' => [
                        'data' => [
                            'type' => 'html_content',
                            'name' => 'html_content',
                            'config' => [
                                'componentType' => 'container',
                                'component' => 'Magento_Ui/js/form/components/html',
                                'content' => ObjectManager::getInstance()->create(
                                    BlockOrderInformation::class
                                )->toHtml()
                            ]
                        ]
                    ]
                ]
            ]
        ];
        return $container;
    }
}
