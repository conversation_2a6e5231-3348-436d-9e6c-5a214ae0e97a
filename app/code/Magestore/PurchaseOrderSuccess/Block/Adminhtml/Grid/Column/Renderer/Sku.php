<?php
/**
 * Copyright © 2016 Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Magestore\PurchaseOrderSuccess\Block\Adminhtml\Grid\Column\Renderer;

use Magento\Backend\Block\Context;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Framework\DataObject;
use Magento\Framework\App\ObjectManager;

/**
 * Class for Sku Renderer
 */
class Sku extends \Magento\Backend\Block\Widget\Grid\Column\Renderer\Text
{
    /**
     * Constructor
     *
     * @param Context $context
     * @param array $data
     */
    public function __construct(
        Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }
    
    /**
     * Renders grid column
     *
     * @param DataObject $row
     * @return string
     */
    public function render(DataObject $row)
    {
        $result = $this->_getValue($row);
        if ($row->getQtyReceived() > 0) {
            $result .= '<span style="color: #ff0000; float: left; width: 100%; clear: both">'
                .__('This product cannot be deleted from the PO, because it has been received at least one qty.')
                .'</span>';
        }
        return $result;
    }
}
