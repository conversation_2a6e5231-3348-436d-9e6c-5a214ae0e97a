<?php
/**
 * Copyright © 2016 Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Magestore\PurchaseOrderSuccess\Block\Adminhtml\Grid\Column\Renderer;

use Magento\Backend\Block\Context;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Framework\DataObject;
use Magento\Framework\App\ObjectManager;

/**
 * Class for Text Renderer
 */
class Text extends \Magento\Backend\Block\Widget\Grid\Column\Renderer\Text
{

    /**
     * @var DataPersistorInterface
     */
    protected $dataPersistor;

    /**
     * Constructor
     *
     * @param Context $context
     * @param DataPersistorInterface $dataPersistor
     * @param array $data
     */
    public function __construct(
        Context $context,
        ?DataPersistorInterface $dataPersistor = null,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->dataPersistor = $dataPersistor ?? ObjectManager::getInstance()->get(DataPersistorInterface::class);
    }

    /**
     * Renders grid column
     *
     * @param DataObject $row
     * @return string
     */
    public function render(DataObject $row)
    {
        if ($this->getColumn()->getEditable()) {
            $errorProducts = $this->dataPersistor->get('error_products');
            $error = false;
            if ($this->getColumn()->getId() === 'qty_orderred') {
                if (!empty($errorProducts) && array_key_exists($row['product_id'], $errorProducts)) {
                    $error = $errorProducts[$row['product_id']];
                }
            }
            $result = '<div class="admin__grid-control">';
            $result .= $this->getColumn()->getEditOnly()
                ? ''
                : '<span class="admin__grid-control-value">' . $this->_getValue($row) . '</span>';

            $result .= $this->_getInputHiddenValueElement($row) .$this->_getInputValueElement($row, $error) ;
            if ($error) {
                $result .= '<div class="error-box">
                    <p>'.$error.'</p>
                </div>';
            }
            $result .= '</div>';
            return $result;
        }
        return $this->_getValue($row);
    }

    /**
     * Get Input Value Element
     *
     * @param DataObject $row
     * @return string
     */
    public function _getInputValueElement(DataObject $row)
    {
        $errorProducts = $this->dataPersistor->get('error_products');
        $errorClass = '';
        if ($this->getColumn()->getId() === 'qty_orderred') {
            if (!empty($errorProducts) && array_key_exists($row['product_id'], $errorProducts)) {
                $errorClass = 'error-field';
            }
        }
        return '<input type="text" style="display: none" class="input-text ' .
            $errorClass.
            $this->getColumn()->getValidateClass() .
            '" name="' .
            $this->getColumn()->getId() .
            '" value="' .
            $this->_getInputValue(
                $row
            ) . '"/>';
    }

    /**
     * Get Input Hidden Value Element
     *
     * @param DataObject $row
     * @return string
     */
    public function _getInputHiddenValueElement(DataObject $row)
    {
        return '<input type="text" style="display: none" class="input-text ' .
            $this->getColumn()->getValidateClass() .
            '" name="' .
            $this->getColumn()->getId() . '_old' .
            '" value="' .
            $this->_getInputValue(
                $row
            ) . '"/>';
    }
}
