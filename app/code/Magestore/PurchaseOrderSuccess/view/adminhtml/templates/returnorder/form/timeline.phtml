<?php
/**
 * Copyright © 2016 Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */

/** @var $block \Magestore\PurchaseOrderSuccess\Block\Adminhtml\ReturnOrder\Edit\Steps\Timeline */
?>
<div data-role="steps-wizard-main" class="steps-wizard <?= /* @noEscape */ $block->getData('config/dataScope')?>" data-bind="scope: '<?= /* @escapeNotVerified */  $block->getComponentName()?>'">
    <div data-role="messages" class="messages"></div>
    <div data-role="steps-wizard-controls" class="steps-wizard-navigation">
        <ul class="nav-bar">
            <?php foreach ($block->getSteps() as $step) { ?>
                <?php $isActive = ''; ?>
                <?php if($step->getComponentName() == $block->getCurrentStep()){
                    $isActive = 'class="active"';
                } ?>
                <li data-role="collapsible" <?php echo $isActive ?> >
                    <a href="#<?= /* @escapeNotVerified */  $step->getComponentName() ?>">
                        <?= /* @escapeNotVerified */  $step->getCaption() ?>
                    </a>
                </li>
            <?php } ?>
        </ul>
    </div>
</div>
<style type="text/css">
    .purchaseordersuccess-purchaseorder-view .steps-wizard {
        width: auto;
        float: left;
    }
    .purchaseordersuccess-purchaseorder-view .steps-wizard .steps-wizard-navigation {
        border-bottom: none;
        border-top: none;
        margin: 0;
        padding: 0;
    }
    .page-actions-buttons .cancel{
        background-color: #d7d4d4;
    }
</style>

