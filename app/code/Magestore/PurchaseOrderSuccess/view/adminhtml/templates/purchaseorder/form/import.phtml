<div id="import-purchase-order-product-modal" class="import-product" 
     data-modal-info="<?php /* @escapeNotVerified */ echo $block->getWidgetOptions();?>">
    <div class="os_panel">
        <div class="header">
            <label><?php echo __('Instruction');?></label>
        </div>
        <div class="content">
            <div class="item">
                <span>
                    <?php echo __('Please choose a CSV file to import products to purchase order. You can download this sample CSV file');?>
                </span>
                <span data-role="data" data-type="text" class="value"></span>
                <div class="clear"></div>
            </div>
            <div class="item">
                <span><a href="<?php echo $block->getCsvSampleLink();?>"><?php echo __('Download'); ?></a></span>
            </div>
        </div>
    </div>
    <div class="import-purchase-order-product-form">
        <?php if ($block->getUseContainer()): ?>
        <form id="import-purchase-order-product-form" class="admin__fieldset" method="post" enctype="multipart/form-data"
              action="<?php /* @escapeNotVerified */ echo $block->getImportUrl();?>">
            <?php endif; ?>
            <input name="form_key" type="hidden" value="<?php /* @escapeNotVerified */ echo $block->getFormKey() ?>" />
            <div class="fieldset admin__field">
                <label for="import_product" class="admin__field-label">
                    <span>
                        <?php /* @escapeNotVerified */ echo __('Please choose  a CSV file to import:') ?>
                    </span>
                </label>
                <div class="admin__field-control">
                    <input type="file" id="import_purchase_order_product_file" 
                           name="import_product" class="input-file required-entry"/>
                </div>
            </div>
            <?php if ($block->getUseContainer()): ?>
        </form>
    <?php endif; ?>
    </div>
</div>
<style type="text/css">
    .os_panel{
        border: 1px #ccc solid;
        margin: 10px;
        border-radius: 10px;
    }

    .os_panel .header{
        border-bottom: 1px #ccc solid;
        padding: 10px;
        background: #373330;
        color: #fff;
        border-radius: 10px 10px 0px 0px;
    }

    .os_panel .content{
        list-style: none;
        margin: 10px;
    }

    .os_panel .content .item{
        padding: 10px 30px;
        text-align: left;
    }

    .os_panel .header label{
        line-height: 32px;
    }

    .os_panel .content .item label,
    .os_panel .header label{
        font-weight: 600;
    }
    
    .mage-error {
        color: red;
    }

    .import-product .admin__field-control{
        margin-top: 5px;
    }
</style>