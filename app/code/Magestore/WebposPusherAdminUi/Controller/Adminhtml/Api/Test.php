<?php
/**
 * Copyright © 2018 Magestore. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Magestore\WebposPusherAdminUi\Controller\Adminhtml\Api;

use Exception;
use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Magento\Framework\Controller\ResultInterface;
use Pusher\Pusher;

/**
 * Class Test
 *
 * Return action results
 */
class Test extends Action implements HttpPostActionInterface
{
    /**
     * @var ResultFactory
     */
    protected $resultFactory;

    /**
     * Constructor
     *
     * @param Context $context
     */
    public function __construct(
        Context $context
    ) {
        $this->resultFactory = $context->getResultFactory();
        parent::__construct($context);
    }

    /**
     * Return result for ajax
     *
     * @return ResultInterface
     */
    public function execute(): ResultInterface
    {
        $response = [
            'message' => '',
            'success' => true,
        ];
        try {
            $appId = $this->getRequest()->getParam('app_id');
            $key = $this->getRequest()->getParam('app_key');
            $secret = $this->getRequest()->getParam('secret');
            $cluster = $this->getRequest()->getParam('cluster');
            $pusher = new Pusher( // phpstan:ignore
                $key,
                $secret,
                $appId,
                [
                    'cluster' => $cluster,
                    'useTLS' => true
                ]
            );
            $pusher->trigger(
                'my_channel',
                'test',
                ['message' => 'Test message']
            );
        } catch (Exception $exception) {
            $response['success'] = false;
            $response['message'] = __('Can not connect to Pusher!');
        }

        $resultJson = $this->resultFactory->create(ResultFactory::TYPE_JSON);
        return $resultJson->setData($response);
    }
}
