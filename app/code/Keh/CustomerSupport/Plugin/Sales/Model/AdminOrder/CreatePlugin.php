<?php
declare(strict_types=1);

namespace Keh\CustomerSupport\Plugin\Sales\Model\AdminOrder;

use Magento\Framework\Exception\LocalizedException;
use Magento\InventorySales\Model\ResourceModel\GetAssignedStockIdForWebsite;
use Magento\Sales\Model\AdminOrder\Create;
use Magento\Store\Model\StoreManagerInterface;

/**
 * Plugin to validate product quantities during admin order creation
 * Ensures quantity doesn't exceed available stock (Stock Available - Reserved)
 */
class CreatePlugin
{
    public function __construct(
        private readonly GetAssignedStockIdForWebsite $getAssignedStockIdForWebsite,
        private readonly StoreManagerInterface $storeManager
    ) {}

    /**
     * Validate product quantities before adding to quote
     *
     * @param Create $subject
     * @param array $products
     * @return array
     * @throws LocalizedException
     */
    public function beforeAddProducts(Create $subject, array $products): array
    {
        $this->validateProductQuantities($products, $subject);
        return [$products];
    }

    /**
     * Validate that requested quantities don't exceed available stock
     *
     * @param array $products
     * @param Create $orderCreate
     * @throws LocalizedException
     */
    private function validateProductQuantities(array $products, Create $orderCreate): void
    {
        $quote = $orderCreate->getQuote();
        $store = $quote->getStore();
        $stockId = $this->getAssignedStockIdForWebsite->execute($store->getWebsite()->getCode());
        
        $connection = $orderCreate->getQuote()->getResource()->getConnection();
        $errors = [];

        foreach ($products as $productId => $productData) {
            if (!isset($productData['qty']) || $productData['qty'] <= 0) {
                continue;
            }

            $requestedQty = (float)$productData['qty'];
            $availableQty = $this->getAvailableQuantity($connection, $productId, $stockId);

            if ($requestedQty > $availableQty) {
                $product = $orderCreate->getSession()->getCatalog()->getProduct($productId);
                $errors[] = __(
                    'Product "%1" (ID: %2): Requested quantity %3 exceeds available stock %4',
                    $product->getName(),
                    $productId,
                    $requestedQty,
                    $availableQty
                );
            }
        }

        if (!empty($errors)) {
            throw new LocalizedException(
                __('Cannot add products to order due to insufficient stock:') . "\n" . implode("\n", $errors)
            );
        }
    }

    /**
     * Get available quantity for a product (Stock Available - Reserved)
     *
     * @param \Magento\Framework\DB\Adapter\AdapterInterface $connection
     * @param int $productId
     * @param int $stockId
     * @return float
     */
    private function getAvailableQuantity($connection, int $productId, int $stockId): float
    {
        // Get product SKU
        $productTable = $connection->getTableName('catalog_product_entity');
        $skuSelect = $connection->select()
            ->from($productTable, 'sku')
            ->where('entity_id = ?', $productId);
        $sku = $connection->fetchOne($skuSelect);

        if (!$sku) {
            return 0;
        }

        // Get stock quantity
        $inventoryStockTable = $connection->getTableName('inventory_stock_' . $stockId);
        $stockSelect = $connection->select()
            ->from($inventoryStockTable, 'quantity')
            ->where('sku = ?', $sku);
        $stockQty = (float)$connection->fetchOne($stockSelect);

        // Get reserved quantity
        $inventoryReservationTable = $connection->getTableName('inventory_reservation');
        $reservationSelect = $connection->select()
            ->from($inventoryReservationTable, new \Zend_Db_Expr('SUM(quantity)'))
            ->where('sku = ?', $sku)
            ->where('stock_id = ?', $stockId);
        $reservedQty = (float)$connection->fetchOne($reservationSelect);

        // Calculate available quantity (Stock - Reserved)
        return max(0, $stockQty - $reservedQty);
    }
}
