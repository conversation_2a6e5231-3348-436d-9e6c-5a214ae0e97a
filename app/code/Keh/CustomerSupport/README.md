# Keh Customer Support
Section for all information needed for customer support team like dashboards and grids

## Features
* Setup Dashboard for Orders, Returns, Exchanges, Reservations.
* Filter grids at once.
* **Admin Order Creation Stock Validation** - Prevents adding more products than available stock when creating orders.

## Admin Order Creation Stock Validation

### Overview
This feature adds quantity validation to the admin order creation process to prevent overselling products.

### How it works:
1. **Stock Columns**: The admin order creation grid displays:
   - **Stock Available Quantity**: Current stock quantity
   - **Reserved Quantity**: Quantity reserved for other orders
   - **Grade**: Product grade information

2. **Validation Logic**:
   - Maximum allowed quantity = Stock Available Quantity - Reserved Quantity
   - Validation occurs when clicking "Add Selected Product(s) to Order" button

3. **Frontend Validation**:
   - JavaScript validation prevents form submission if quantities exceed available stock
   - Shows detailed error message with stock information

4. **Backend Validation**:
   - Server-side plugin validates quantities before adding to quote
   - Provides detailed error messages if validation fails

### Usage:
1. Navigate to Sales > Orders > Create New Order
2. Select customer and click "Add Products"
3. The grid shows Stock Available Quantity and Reserved Quantity columns
4. Select products and enter quantities
5. Click "Add Selected Product(s) to Order"
6. If any quantity exceeds available stock, an error message will appear
