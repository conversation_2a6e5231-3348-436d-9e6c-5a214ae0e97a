<?php
declare(strict_types=1);

namespace Keh\CustomerSupport\Block\Adminhtml\Widget\Grid\Column;

use Keh\CustomerSupport\Model\ResourceModel\GetReservedQuantity;
use Magento\Backend\Block\Context;

class ReservationRenderer extends \Magento\Backend\Block\Widget\Grid\Column\Renderer\AbstractRenderer
{
    public function __construct(
        Context $context,
        private readonly GetReservedQuantity $getReservedQuantity,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    public function render(\Magento\Framework\DataObject $row)
    {
        try {
            $data = $this->getReservedQuantity->execute([$row->getData('sku')]);

            if (empty($data) || empty($data[$row->getData('sku')])) {
                return '';
            }

            $column = '<ul style="list-style-type: none; margin: 0; padding: 0;">';

            foreach ($data[$row->getData('sku')] ?? [] as $stockId => $qty) {
                if ($qty != 0) {
                    $column .= sprintf('<li><strong>%s</strong>: %s</li>', $stockId, abs((int)$qty));
                }
            }

            return $column . '</ul>';
        } catch (\Exception) {
            return '';
        }
    }
}
