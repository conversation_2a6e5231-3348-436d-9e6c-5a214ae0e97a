<?php
declare(strict_types=1);

namespace Keh\CustomerSupport\Block\Adminhtml\Order\Create\Search\Grid\Column\Filter;

use Magento\Backend\Block\Context;
use Magento\Backend\Block\Widget\Grid\Column\Filter\Select;
use Magento\Catalog\Model\Product\Attribute\Repository;
use Magento\Framework\DB\Helper;
use Magento\Framework\Exception\NoSuchEntityException;

class Grade extends Select
{
    private const string ATTRIBUTE_CODE = 'keh_grade';

    public function __construct(
        Context $context,
        Helper $resourceHelper,
        private readonly Repository $attributeRepository,
        array $data = []
    ) {
        parent::__construct($context, $resourceHelper, $data);

    }

    /**
     * Get grid options
     *
     * @return array
     */
    protected function _getOptions()
    {
        try {
            return $this->attributeRepository->get(self::ATTRIBUTE_CODE)->getOptions();
        } catch (NoSuchEntityException) {
            return [];
        }
    }
}
