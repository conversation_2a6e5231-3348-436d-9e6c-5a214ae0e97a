<?php
declare(strict_types=1);

namespace Keh\CustomerSupport\Block\Adminhtml\Order\Create\Search;

use Keh\CustomerSupport\Block\Adminhtml\Order\Create\Search\Grid\Column\Filter\Grade;
use Keh\CustomerSupport\Block\Adminhtml\Order\Create\Search\Grid\Renderer\ProductAttribute;
use Magento\Backend\Block\Template\Context;
use Magento\Backend\Helper\Data;
use Magento\Backend\Model\Session\Quote;
use Magento\Catalog\Model\ProductFactory;
use Magento\Catalog\Model\ResourceModel\Product\Collection;
use Magento\InventorySales\Model\ResourceModel\GetAssignedStockIdForWebsite;
use Magento\Sales\Block\Adminhtml\Order\Create\Search\Grid\DataProvider\ProductCollection;
use Magento\Sales\Model\Config;

/**
 * Add columns to the products grid in the order creation page of the admin.
 */
class Grid extends \Magento\Sales\Block\Adminhtml\Order\Create\Search\Grid
{
    public function __construct(
        Context $context,
        Data $backendHelper,
        ProductFactory $productFactory,
        \Magento\Catalog\Model\Config $catalogConfig,
        Quote $sessionQuote,
        Config $salesConfig,
        private readonly GetAssignedStockIdForWebsite $getAssignedStockIdForWebsite,
        private readonly ProductCollection $productCollectionProvider,
        array $data = []
    ) {
        parent::__construct(
            $context,
            $backendHelper,
            $productFactory,
            $catalogConfig,
            $sessionQuote,
            $salesConfig,
            $data,
            $productCollectionProvider
        );
    }

    protected function _prepareCollection()
    {
        $collection = $this->productCollectionProvider
            ->getCollectionForStore($this->getStore())
            ->addAttributeToSelect(
                $this->_catalogConfig->getProductAttributes()
            )
            ->addAttributeToFilter(
                'type_id',
                $this->_salesConfig->getAvailableProductTypes()
            );

        $this->setCollection($collection);

        return \Magento\Backend\Block\Widget\Grid::_prepareCollection();
    }

    protected function _prepareColumns()
    {
        $this->addColumnAfter(
            'keh_grade',
            [
                'header' => __('Grade'),
                'renderer' => ProductAttribute::class,
                'index' => 'keh_grade',
                'type' => 'select',
                'filter' => Grade::class
            ],
            'name'
        );

        $this->addColumnAfter(
            'salable_quantity',
            [
                'header' => __('Salable Quantity'),
                'component' => 'Magento_InventorySalesAdminUi/js/product/grid/cell/salable-quantity',
                'renderer' => \Magestore\Customercredit\Block\Adminhtml\Creditproduct\Column\Renderer\SalableQuantity::class,
                'index' => 'salable_quantity',
                'filter' => false
            ],
            'price'
        );

        $this->addColumnAfter(
            'reserved_quantity',
            [
                'header' => __('Reserved Quantity'),
                'renderer' => \Keh\CustomerSupport\Block\Adminhtml\Widget\Grid\Column\ReservationRenderer::class,
                'index' => 'reserved_quantity',
                'bodyTmpl' => 'ui/grid/cells/html',
                'filter' => false
            ],
            'salable_quantity'
        );

        return parent::_prepareColumns();
    }
}
