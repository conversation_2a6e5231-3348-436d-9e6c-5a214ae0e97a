/**
 * Basic styles for quantity validation in admin order creation
 */

/* Ensure quantity columns are properly aligned */
.admin__data-grid-wrap .data-grid .col-stock_item_qty,
.admin__data-grid-wrap .data-grid .col-reservation_qty {
    text-align: center;
}

/* Style for quantity input validation errors (if needed for future enhancements) */
.qty-validation-error {
    border-color: #e22626 !important;
    background-color: #fff5f5 !important;
}
