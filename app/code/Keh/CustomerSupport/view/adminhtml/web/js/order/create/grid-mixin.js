define([
    'jquery',
    'Magento_Ui/js/modal/alert',
    'mage/translate',
    'prototype'
], function ($, alert, $t) {
    'use strict';

    function validateSelectedProducts() {
        let errors = [];
        const $checkedProducts = $('#sales_order_create_search_grid_table .admin__control-checkbox:checked');

        $checkedProducts.each(function () {
            const $row = $(this).closest('tr');
            const $qtyInput = $row.find('input[name="qty"]');

            if ($qtyInput.length && parseFloat($qtyInput.val()) > 0) {
                const validation = validateProductQuantity($row, $qtyInput);
                if (!validation.valid) {
                    errors.push(validation.error);
                }
            }
        });

        if (errors.length > 0) {
            showValidationErrors(errors);
            return false;
        }

        return true;
    }

    function validateProductQuantity($row, $qtyInput) {
        const requestedQty = parseFloat($qtyInput.val()) || 0;
        const stockQty = getStockQuantity($row);
        const reservedQty = getReservedQuantity($row);
        const availableQty = Math.max(0, stockQty - reservedQty);

        if (requestedQty > availableQty) {
            return {
                valid: false,
                error: {
                    product: getProductName($row),
                    requested: requestedQty,
                    available: availableQty,
                    stock: stockQty,
                    reserved: reservedQty,
                    inputElement: $qtyInput
                }
            };
        }

        return { valid: true };
    }

    function getProductName($row) {
        return $row.find('td.col-name').text().trim() || 'Unknown Product';
    }

    function getStockQuantity($row) {
        return parseFloat($row.find('td.col-stock_item_qty').text().trim()) || 0;
    }

    function getReservedQuantity($row) {
        return parseFloat($row.find('td.col-reservation_qty').text().trim()) || 0;
    }

    function showValidationErrors(errors) {
        let message = $t('Cannot add products to order due to insufficient stock:') + '\n\n';

        errors.forEach(function (error) {
            message += $t('• %1: Requested %2, but only %3 available (Stock: %4, Reserved: %5)')
                .replace('%1', error.product)
                .replace('%2', error.requested)
                .replace('%3', error.available)
                .replace('%4', error.stock)
                .replace('%5', error.reserved) + '\n';
        });

        alert({
            title: $t('Insufficient Stock'),
            content: message.replace(/\n/g, '<br>'),
            actions: {
                always: function () {
                    if (errors.length > 0 && errors[0].inputElement) {
                        errors[0].inputElement.focus().select();
                    }
                }
            }
        });
    }

    function patchWhenReady() {
        const maxAttempts = 50;
        let attempts = 0;

        const interval = setInterval(function () {
            if (window.order && typeof window.order.productGridAddSelected === 'function' && !window.order.isValidationPatched) {
                const originalFunc = window.order.productGridAddSelected;

                window.order.productGridAddSelected = function () {
                    if (validateSelectedProducts()) {
                        originalFunc.apply(this, arguments);
                    }
                };

                window.order.isValidationPatched = true;

                clearInterval(interval);
            }

            attempts++;
            if (attempts >= maxAttempts) {
                clearInterval(interval);
            }
        }, 100);
    }

    patchWhenReady();

    return function (target) {
        return target;
    };
});
