/**
 * Quantity validation for admin order creation grid
 * Validates on "Add Selected Product(s) to Order" button click
 * Prevents adding more products than available stock (Stock Available - Reserved)
 */
define([
    'jquery',
    'Magento_Ui/js/modal/alert',
    'mage/translate'
], function ($, alert, $t) {
    'use strict';

    return function (config) {

        /**
         * Initialize quantity validation using event delegation
         */
        function init() {
            // Use event delegation to handle buttons that may not exist yet or are hidden
            // Remove any existing handlers first
            $(document).off('click.qty-validation', '.action-add');

            // Add event delegation for all .action-add buttons
            $(document).on('click.qty-validation', '.action-add', function(e) {
                var button = $(this);

                // Check if this is the "Add Selected Product(s) to Order" button in the search grid
                // Look for the button that's within the order-search container and has the right text
                if (button.closest('#order-search').length > 0) {
                    var buttonText = button.text().trim();
                    if (buttonText.indexOf('Add Selected Product') !== -1 ||
                        buttonText.indexOf('Add Products') !== -1) {

                        if (!validateSelectedProducts()) {
                            e.preventDefault();
                            e.stopPropagation();
                            return false;
                        }
                    }
                }
            });
        }

        /**
         * Validate all selected products
         */
        function validateSelectedProducts() {
            var isValid = true;
            var errors = [];

            // Find all checked product checkboxes in the search grid
            // Look in multiple possible locations for the grid table
            var searchSelectors = [
                '#order-search table input[type="checkbox"]:checked',
                '#sales_order_create_search_grid_table input[type="checkbox"]:checked',
                '.admin__data-grid-wrap input[type="checkbox"]:checked'
            ];

            var checkedBoxes = $();
            for (var i = 0; i < searchSelectors.length; i++) {
                checkedBoxes = $(searchSelectors[i]);
                if (checkedBoxes.length > 0) {
                    break;
                }
            }

            checkedBoxes.each(function() {
                var checkbox = $(this);
                var row = checkbox.closest('tr');
                var qtyInput = row.find('input[name="qty"]');

                if (qtyInput.length) {
                    var validation = validateProductQuantity(row, qtyInput);
                    if (!validation.valid) {
                        isValid = false;
                        errors.push(validation.error);
                    }
                }
            });

            // Show errors if any
            if (!isValid) {
                showValidationErrors(errors);
            }

            return isValid;
        }

        /**
         * Validate quantity for a single product
         */
        function validateProductQuantity(row, qtyInput) {
            var qty = parseFloat(qtyInput.val()) || 0;
            var productName = row.find('td').eq(getColumnIndex('name')).text().trim();
            var stockQty = parseFloat(row.find('td').eq(getColumnIndex('stock_item_qty')).text()) || 0;
            var reservedQty = parseFloat(row.find('td').eq(getColumnIndex('reservation_qty')).text()) || 0;
            var maxAvailable = Math.max(0, stockQty - reservedQty);

            if (qty > maxAvailable) {
                return {
                    valid: false,
                    error: {
                        product: productName,
                        requested: qty,
                        available: maxAvailable,
                        stock: stockQty,
                        reserved: reservedQty
                    }
                };
            }

            return { valid: true };
        }

        /**
         * Get column index by header text
         */
        function getColumnIndex(columnName) {
            var headerMap = {
                'name': ['Product', 'Name'],
                'stock_item_qty': 'Stock Available Quantity',
                'reservation_qty': 'Reserved Quantity'
            };

            var possibleHeaders = headerMap[columnName] || [];
            var index = -1;

            // Look for headers in the search grid table
            var tableSelectors = [
                '#order-search table th',
                '#sales_order_create_search_grid_table th',
                '.admin__data-grid-wrap th'
            ];

            for (var s = 0; s < tableSelectors.length && index === -1; s++) {
                $(tableSelectors[s]).each(function(i) {
                    var headerText = $(this).text().trim();
                    for (var h = 0; h < possibleHeaders.length; h++) {
                        if (headerText === possibleHeaders[h]) {
                            index = i;
                            return false;
                        }
                    }
                });
            }

            return index;
        }

        /**
         * Show validation errors to user
         */
        function showValidationErrors(errors) {
            var message = $t('Cannot add products to order due to insufficient stock:') + '\n\n';

            errors.forEach(function(error) {
                message += $t('• %1: Requested %2, but only %3 available (Stock: %4, Reserved: %5)')
                    .replace('%1', error.product)
                    .replace('%2', error.requested)
                    .replace('%3', error.available)
                    .replace('%4', error.stock)
                    .replace('%5', error.reserved) + '\n';
            });

            alert({
                title: $t('Insufficient Stock'),
                content: message.replace(/\n/g, '<br>'),
                actions: {
                    always: function() {
                        if (errors.length > 0) {
                            var searchSelectors = [
                                '#order-search table input[type="checkbox"]:checked',
                                '#sales_order_create_search_grid_table input[type="checkbox"]:checked',
                                '.admin__data-grid-wrap input[type="checkbox"]:checked'
                            ];

                            for (var i = 0; i < searchSelectors.length; i++) {
                                var firstErrorRow = $(searchSelectors[i]).first().closest('tr');
                                var qtyInput = firstErrorRow.find('input[name="qty"]');
                                if (qtyInput.length) {
                                    qtyInput.focus().select();
                                    break;
                                }
                            }
                        }
                    }
                }
            });
        }

        init();
    };
});
