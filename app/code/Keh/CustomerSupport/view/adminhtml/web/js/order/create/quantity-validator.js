/**
 * Quantity validation for admin order creation grid
 * Validates on "Add Selected Product(s) to Order" button click
 * Prevents adding more products than available stock (Stock Available - Reserved)
 */
define([
    'jquery',
    'Magento_Ui/js/modal/alert',
    'mage/translate'
], function ($, alert, $t) {
    'use strict';

    return function (config) {
        
        /**
         * Initialize quantity validation
         */
        function init() {
            // Find the "Add Selected Product(s) to Order" button
            var addButton = $('#id_' + config.htmlId + '_addButton');
            
            if (addButton.length) {
                // Override the button click event
                addButton.off('click.qty-validation');
                addButton.on('click.qty-validation', function(e) {
                    if (!validateSelectedProducts()) {
                        e.preventDefault();
                        e.stopPropagation();
                        return false;
                    }
                });
            }
        }

        /**
         * Validate all selected products
         */
        function validateSelectedProducts() {
            var isValid = true;
            var errors = [];
            
            // Find all checked product checkboxes
            $('#' + config.htmlId + '_table input[type="checkbox"]:checked').each(function() {
                var checkbox = $(this);
                var row = checkbox.closest('tr');
                var qtyInput = row.find('input[name="qty"]');
                
                if (qtyInput.length) {
                    var validation = validateProductQuantity(row, qtyInput);
                    if (!validation.valid) {
                        isValid = false;
                        errors.push(validation.error);
                    }
                }
            });
            
            // Show errors if any
            if (!isValid) {
                showValidationErrors(errors);
            }
            
            return isValid;
        }

        /**
         * Validate quantity for a single product
         */
        function validateProductQuantity(row, qtyInput) {
            var qty = parseFloat(qtyInput.val()) || 0;
            var productName = row.find('td').eq(getColumnIndex('name')).text().trim();
            var stockQty = parseFloat(row.find('td').eq(getColumnIndex('stock_item_qty')).text()) || 0;
            var reservedQty = parseFloat(row.find('td').eq(getColumnIndex('reservation_qty')).text()) || 0;
            var maxAvailable = Math.max(0, stockQty - reservedQty);
            
            if (qty > maxAvailable) {
                return {
                    valid: false,
                    error: {
                        product: productName,
                        requested: qty,
                        available: maxAvailable,
                        stock: stockQty,
                        reserved: reservedQty
                    }
                };
            }
            
            return { valid: true };
        }

        /**
         * Get column index by header text
         */
        function getColumnIndex(columnName) {
            var headerMap = {
                'name': 'Product',
                'stock_item_qty': 'Stock Available Quantity',
                'reservation_qty': 'Reserved Quantity'
            };
            
            var headerText = headerMap[columnName];
            var index = -1;
            
            if (headerText) {
                $('#' + config.htmlId + '_table th').each(function(i) {
                    if ($(this).text().trim() === headerText) {
                        index = i;
                        return false;
                    }
                });
            }
            
            return index;
        }

        /**
         * Show validation errors to user
         */
        function showValidationErrors(errors) {
            var message = $t('Cannot add products to order due to insufficient stock:') + '\n\n';
            
            errors.forEach(function(error) {
                message += $t('• %1: Requested %2, but only %3 available (Stock: %4, Reserved: %5)')
                    .replace('%1', error.product)
                    .replace('%2', error.requested)
                    .replace('%3', error.available)
                    .replace('%4', error.stock)
                    .replace('%5', error.reserved) + '\n';
            });
            
            alert({
                title: $t('Insufficient Stock'),
                content: message.replace(/\n/g, '<br>'),
                actions: {
                    always: function() {
                        // Focus on first invalid quantity input
                        if (errors.length > 0) {
                            var firstErrorRow = $('#' + config.htmlId + '_table input[type="checkbox"]:checked').first().closest('tr');
                            var qtyInput = firstErrorRow.find('input[name="qty"]');
                            if (qtyInput.length) {
                                qtyInput.focus().select();
                            }
                        }
                    }
                }
            });
        }

        // Initialize when DOM is ready
        $(document).ready(function() {
            init();
        });
        
        // Re-initialize when grid is reloaded
        $(document).on('contentUpdated', function() {
            init();
        });
    };
});
