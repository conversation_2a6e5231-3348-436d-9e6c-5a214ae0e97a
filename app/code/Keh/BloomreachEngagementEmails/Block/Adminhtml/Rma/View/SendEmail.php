<?php

declare(strict_types=1);

namespace Keh\BloomreachEngagementEmails\Block\Adminhtml\Rma\View;

class SendEmail extends \Mirasvit\Rma\Block\Adminhtml\Rma\Edit
{
    protected function _construct()
    {
        parent::_construct();

        $rma = $this->getRma();
        if ($rma && $rma->getId()) {
            $this->buttonList->add(
                'send_email',
                [
                    'label' => __('Send Invoice'),
                    'onclick' => 'confirmSetLocation(\'' . __('Are you sure you want to send an Invoice email to customer?') . '\', \'' . $this->getUrl('keh_rma_email/rma/sendEmail', ['rma_id' => $rma->getId()]) . '\')',
                    'class' => 'send-email secondary'
                ],
                -1
            );
        }

        return $this;
    }
}
