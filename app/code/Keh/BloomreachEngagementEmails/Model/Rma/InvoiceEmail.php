<?php

declare(strict_types=1);

namespace Keh\BloomreachEngagementEmails\Model\Rma;

use Keh\BloomreachEngagementEmails\Model\AbstractEmailService;

/**
 * Send rma request invoice email through Bloomreach Engagement email campaign
 */
readonly class InvoiceEmail extends AbstractEmailService
{
    protected function getTemplateId(): string
    {
        return $this->emailsConfigProvider->getRmaRequestInvoiceTemplateId();
    }

    protected function getCampaignName(): string
    {
        return 'RMA Notification';
    }

    protected function getBodyProperties($rma, string $templateId): array
    {
        $rmaArray = [];

        return [
            'campaign_name' => $this->getCampaignName(),
            'recipient' => [
                'customer_ids' => [
                    'email_id' => $rma->getEmail(),
                ],
                'email' => $rma->getEmail()
            ],
            'email_content' => [
                'template_id' => $templateId,
                'params' => [
                    'order' => [
                        'doc_id' => $rma->getOrderId(),
                        'order_date' => $rma->getCreatedAt(),
                        'rma_number' => $rma->getIncrementId(),
                        'items' => array_map(function($item) {
                            return [
                                'model_name' => $item->getName(),
                                'grade_tag' => '',
                                'attributes' => '',
                                'sku' => $item->getSku(),
                                'quantity' => (int)$item->getQtyOrdered()
                            ];
                        }, $rmaArray),
                    ],
                    'customer' => [
                        'first_name' => $rma->getFirstname(),
                        'last_name' => $rma->getLastname(),
                        'address' => [
                            'billing_address' => [
                                'address_line_1' => $rma->getStreet(),
                                'address_line_2' => '',
                                'city' => $rma->getCity(),
                                'state' => $rma->getRegion(),
                                'postal_code' => $rma->getPostcode(),
                                'phone' => $rma->getTelephone(),
                            ]
                        ]
                    ]
                ]
            ],
            'settings' => [
                'transfer_identity' => 'enabled'
            ]
        ];
    }
}
