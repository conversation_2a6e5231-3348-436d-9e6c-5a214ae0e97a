<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="bloomreach" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
            <group id="engagement_emails" sortOrder="55" translate="label" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Engagement Emails</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Emails Integration</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="api_target" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>API Target</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                    <validate>required-entry validate-url</validate>
                </field>
                <field id="project_token" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Project Token</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                    <validate>required-entry validate-no-empty</validate>
                </field>
                <field id="private_api_key" translate="label" type="text" sortOrder="25" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Private Api Key</label>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                    <validate>required-entry validate-no-empty</validate>
                </field>
                <field id="private_api_secret" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Private Api Secret</label>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                    <validate>required-entry validate-no-empty</validate>
                </field>
                <field id="gift_card_capture_template_id" translate="label" type="text" sortOrder="31" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Gift card 'capture' template id</label>
                    <comment>From Bloomreach Engagement Emails integration (leave empty to skip email sending)</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="gift_card_hold_template_id" translate="label" type="text" sortOrder="32" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Gift card 'hold' template id</label>
                    <comment>From Bloomreach Engagement Emails integration (leave empty to skip email sending)</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="gift_card_cancel_template_id" translate="label" type="text" sortOrder="33" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Gift card 'cancel' template id</label>
                    <comment>From Bloomreach Engagement Emails integration (leave empty to skip email sending)</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="customer_reset_password_template_id" translate="label" type="text" sortOrder="33" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Customer reset password template id</label>
                    <comment>From Bloomreach Engagement Emails integration (leave empty to skip email sending)</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="customer_reset_password_confirmation_template_id" translate="label" type="text" sortOrder="34" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Customer password reset confirmation template id</label>
                    <comment>From Bloomreach Engagement Emails integration (leave empty to skip email sending)</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="customer_account_confirmation_template_id" translate="label" type="text" sortOrder="35" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Customer account confirmation template id</label>
                    <comment>From Bloomreach Engagement Emails integration (leave empty to skip email sending)</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="rma_request_invoice_template_id" translate="label" type="text" sortOrder="35" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Rma request invoice template id</label>
                    <comment>From Bloomreach Engagement Emails integration (leave empty to skip email sending)</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="customer_update_confirmation_template_id" translate="label" type="text" sortOrder="36" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Customer update confirmation template id</label>
                    <comment>From Bloomreach Engagement Emails integration (leave empty to skip email sending)</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="admin_reset_password_template_id" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Admin reset password template id</label>
                    <comment>From Bloomreach Engagement Emails integration (leave empty to skip email sending)</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
