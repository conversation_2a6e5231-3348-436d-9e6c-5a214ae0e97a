<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <!-- Preference -->
    <preference for="Keh\BloomreachEngagementEmails\Api\Data\ApiEmailsRequestInterface" type="Keh\BloomreachEngagementEmails\Model\Data\ApiEmailsRequest"/>

    <!-- Use Bloomreach emails instead of Mailjet -->
    <type name="Inchoo\GiftCard\Observer\ProcessOrder">
        <arguments>
            <argument name="email" xsi:type="object">Keh\BloomreachEngagementEmails\Model\GiftCard\EmailSender</argument>
        </arguments>
    </type>
    <type name="Inchoo\GiftCard\Model\GiftCard">
        <arguments>
            <argument name="emailManagement" xsi:type="object">Keh\BloomreachEngagementEmails\Model\GiftCard\TradeCreditEmail</argument>
        </arguments>
    </type>
    <type name="Inchoo\GiftCard\Plugin\Giftcard\Model\Service\GiftCardManagementPlugin">
        <arguments>
            <argument name="tradeCreditEmail" xsi:type="object">Keh\BloomreachEngagementEmails\Model\GiftCard\TradeCreditEmail</argument>
        </arguments>
    </type>
    <type name="Magento\Customer\Model\EmailNotificationInterface">
        <plugin name="keh_bloomreach_engagement_emails_send_forgot_password_email"  type="Keh\BloomreachEngagementEmails\Plugin\Magento\Customer\Model\EmailNotificationPlugin"/>
    </type>
    <type name="Magento\Customer\Model\AccountManagement">
        <plugin name="keh_bloomreach_engagement_emails_send_password_reset_confirmation_email" type="Keh\BloomreachEngagementEmails\Plugin\Magento\Customer\Model\AccountManagementPlugin"/>
    </type>
    <type name="Magento\User\Model\Spi\NotificatorInterface">
        <plugin name="keh_bloomreach_engagement_emails_send_admin_forgot_password_email" type="Keh\BloomreachEngagementEmails\Plugin\Magento\User\Model\NotificatorPlugin"/>
    </type>
</config>
