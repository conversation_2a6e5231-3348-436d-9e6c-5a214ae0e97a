<?php
declare(strict_types=1);

namespace Keh\NetSuite\Model\DataProvider\Kraken;

use Google\Protobuf\Timestamp;
use Google\Protobuf\TimestampFactory;
use Kraken\V1\SalesOrderFulfillmentStage as KrakenOrderFulfillmentStage;
use Kraken\V1\SalesOrderFulfillmentStageFactory as KrakenOrderFulfillmentStageFactory;
use Magento\Sales\Model\Order;

readonly class OrderFulfillmentStage
{
    public function __construct(
        private KrakenOrderFulfillmentStageFactory $krakenOrderFulfillmentStageFactory,
        private TimestampFactory $googleTimestampFactory
    ) {

    }


    public function provide(string $orderStatus): KrakenOrderFulfillmentStage
    {
        /** @var Timestamp $createdAt */
        $updatedAt = $this->googleTimestampFactory->create();
        $updatedAt->fromDateTime(new \DateTime());

        /** @var KrakenOrderFulfillmentStage $krakenOrderFulfillmentStage */
        $krakenOrderFulfillmentStage = $this->krakenOrderFulfillmentStageFactory->create();
        // TODO - adjust the logic
        $krakenOrderFulfillmentStage
            ->setStageName(Order::STATE_COMPLETE === $orderStatus ? 'packed' : 'packing')
            ->setStatus(Order::STATE_COMPLETE === $orderStatus ? 'done' : 'in_progress')
            ->setUpdatedAt($updatedAt);

        return $krakenOrderFulfillmentStage;
    }
}
