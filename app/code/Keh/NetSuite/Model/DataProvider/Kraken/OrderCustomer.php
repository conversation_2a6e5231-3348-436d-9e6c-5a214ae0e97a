<?php
declare(strict_types=1);

namespace Keh\NetSuite\Model\DataProvider\Kraken;

use Keh\NetSuite\Model\DataProvider\Kraken\OrderAddress as KrakenOrderAddress;
use Kraken\V1\SalesOrderCustomer as KrakenOrderCustomer;
use Kraken\V1\SalesOrderCustomerFactory as KrakenOrderCustomerFactory;
use Magento\Sales\Api\Data\OrderInterface;

readonly class OrderCustomer
{
    public function __construct(
        private KrakenOrderCustomerFactory $krakenOrderCustomerFactory,
        private KrakenOrderAddress $krakenOrderAddress,
    ) {
    }

    public function provide(OrderInterface $magentoOrder): KrakenOrderCustomer
    {
        $address = $magentoOrder->getShippingAddress() ?: $magentoOrder->getBillingAddress();
        /** @var KrakenOrderCustomer $krakenOrderCustomer */
        $krakenOrderCustomer = $this->krakenOrderCustomerFactory->create();
        $krakenOrderCustomer
            ->setCustomerId((string)$magentoOrder->getCustomerId())
            ->setEmail((string)$magentoOrder->getCustomerEmail())
            ->setPhone((string)$address->getTelephone())
            ->setFirstName((string)$address->getFirstname())
            ->setLastName((string)$address->getLastname());
        if ($magentoOrder->getShippingAddress()) {
            $krakenOrderCustomer->setShippingAddress(
                $this->krakenOrderAddress->provide($magentoOrder->getShippingAddress())
            );
        }
        if ($magentoOrder->getBillingAddress()) {
            $krakenOrderCustomer->setBillingAddress(
                $this->krakenOrderAddress->provide($magentoOrder->getBillingAddress())
            );
        }

        return $krakenOrderCustomer;
    }
}
