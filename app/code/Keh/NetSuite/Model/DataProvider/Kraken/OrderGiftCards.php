<?php
declare(strict_types=1);

namespace Keh\NetSuite\Model\DataProvider\Kraken;

use Aheadworks\Giftcard\Api\Data\GiftcardInterface;
use Aheadworks\Giftcard\Api\GiftcardRepositoryInterface;
use Keh\NetSuite\Logger\Logger;
use <PERSON><PERSON>en\V1\SalesOrderGiftCards as KrakenOrderGiftCards;
use Kraken\V1\SalesOrderGiftCardsFactory as KrakenOrderGiftCardsFactory;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Sales\Api\Data\OrderInterface;

readonly class OrderGiftCards
{
    public function __construct(
        private KrakenOrderGiftCardsFactory $krakenOrderGiftCardsFactory,
        private GiftcardRepositoryInterface $giftCardRepository,
        private Logger $logger
    ) {

    }

    /**
     * @param OrderInterface $magentoOrder
     * @return KrakenOrderGiftCards[]
     */
    public function provide(OrderInterface $magentoOrder): array
    {
        $result = [];
        $giftCardCodes = $magentoOrder->getExtensionAttributes()?->getAwGiftcardCodes();
        if ($giftCardCodes && is_array($giftCardCodes)) {
            foreach ($giftCardCodes as $giftCardCode) {
                /** @var KrakenOrderGiftCards $krakenOrderGiftCard */
                $krakenOrderGiftCard = $this->krakenOrderGiftCardsFactory->create();
                $krakenOrderGiftCard
                    ->setGiftCardCode((string)$giftCardCode->getGiftCardCode())
                    ->setGiftCardAmount((float)$giftCardCode->getGiftCardAmount())
                    ->setGiftCardTransactionId((string)$giftCardCode->getGiftcardId())
                    ->setRemainingBalance(0);
                $awGiftCard = $this->getAwGiftCardById((int)$giftCardCode->getGiftcardId());
                if ($awGiftCard) {
                    $krakenOrderGiftCard->setRemainingBalance((float)$awGiftCard->getBalance());
                }
                $result[] = $krakenOrderGiftCard;
            }
        }

        return $result;
    }

    /**
     * @param int $giftCardId
     * @return GiftcardInterface|null
     */
    private function getAwGiftCardById(int $giftCardId): ?GiftcardInterface
    {
        try {
            return $this->giftCardRepository->get($giftCardId);
        } catch (NoSuchEntityException $exception) {
            $this->logger->error(sprintf(
                "%s, failed to load aw_giftcard model, id: %s, error: %s",
                __METHOD__,
                $giftCardId,
                $exception->getMessage()
            ));
            return null;
        }
    }
}
