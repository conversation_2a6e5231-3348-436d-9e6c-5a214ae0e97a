<?php
declare(strict_types=1);

namespace Keh\NetSuite\Model\DataProvider\Kraken;

use Keh\NetSuite\Logger\Logger;
use Kraken\V1\SalesOrderItemDiscount as KrakenOrderItemDiscount;
use <PERSON>raken\V1\SalesOrderItemDiscountFactory as KrakenOrderItemDiscountFactory;
use Magento\Sales\Api\Data\OrderItemInterface;
use Magento\SalesRule\Model\ResourceModel\Rule\CollectionFactory;

readonly class OrderItemDiscount
{
    public function __construct(
        private KrakenOrderItemDiscountFactory $krakenOrderItemDiscountFactory,
        private CollectionFactory $magentoSalesRuleCollectionFactory,
        private Logger $logger
    ) {

    }

    /**
     * @param OrderItemInterface $orderItem
     * @return KrakenOrderItemDiscount[]
     */
    public function provide(OrderItemInterface $orderItem): array
    {
        $result = [];
        if ($orderItem->getAppliedRuleIds()) {
            $salesRules = $this->getSalesRules($orderItem, explode(',', $orderItem->getAppliedRuleIds()));
            foreach ($salesRules as $rule) {
                /** @var KrakenOrderItemDiscount $krakenOrderItemDiscount */
                $krakenOrderItemDiscount = $this->krakenOrderItemDiscountFactory->create();
                $krakenOrderItemDiscount
                    ->setDiscountCode((string)$rule->getName())
                    ->setDiscountAmount((float)$rule->getDiscountAmount())
                    ->setDiscountType((string)$rule->getSimpleAction())
                    ->setDiscountSource('promo')
                    ->setAppliedBeforeTax(true);

                $result[] = $krakenOrderItemDiscount;
            }
        }

        return $result;
    }

    /**
     * Load from salesrule table.
     *
     * @param OrderItemInterface $orderItem
     * @param array $appliedRuleIds
     * @return array
     */
    private function getSalesRules(OrderItemInterface $orderItem, array $appliedRuleIds): array
    {
        try {
            $rules = $this->magentoSalesRuleCollectionFactory->create()
                ->addFieldToFilter('rule_id', ['in' => $appliedRuleIds]);
            return $rules->getItems();
        } catch (\Exception $exception) {
            $this->logger->error(sprintf(
                "%s, failed to load catalog rules: %s, error: %s",
                __METHOD__,
                implode(',', $appliedRuleIds),
                $exception->getMessage()
            ));
            return [];
        }
    }
}
