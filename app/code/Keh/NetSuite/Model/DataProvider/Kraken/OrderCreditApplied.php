<?php
declare(strict_types=1);

namespace Keh\NetSuite\Model\DataProvider\Kraken;

use Kraken\V1\SalesOrderCreditApplied as KrakenOrderCreditApplied;
use Kraken\V1\SalesOrderCreditAppliedFactory as KrakenOrderCreditAppliedFactory;
use Magento\Sales\Api\Data\OrderInterface;

readonly class OrderCreditApplied
{
    public function __construct(
        private KrakenOrderCreditAppliedFactory $krakenOrderCreditAppliedFactory
    ) {

    }

    /**
     * @param OrderInterface $magentoOrder
     * @return KrakenOrderCreditApplied[]
     */
    public function provide(OrderInterface $magentoOrder): array
    {
        $result = [];
        if ($magentoOrder->getCustomercreditDiscount() > 0) {
            /** @var KrakenOrderCreditApplied $krakenOrderCreditApplied */
            $krakenOrderCreditApplied = $this->krakenOrderCreditAppliedFactory->create();
            $krakenOrderCreditApplied
                ->setCreditAmount((float)$magentoOrder->getCustomercreditDiscount())
                ->setCreditReferenceId(0.00) // no such id in magento
                ->setCreditType('store_credit');
        }
        return $result;
    }
}
