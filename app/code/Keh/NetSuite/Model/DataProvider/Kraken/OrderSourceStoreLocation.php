<?php
declare(strict_types=1);

namespace Keh\NetSuite\Model\DataProvider\Kraken;

use Kraken\V1\SalesOrderSourceStoreLocation as KrakenOrderSourceStoreLocation;
use <PERSON>raken\V1\SalesOrderSourceStoreLocationFactory as KrakenOrderSourceStoreLocationFactory;

class OrderSourceStoreLocation
{
    private ?KrakenOrderSourceStoreLocation $cachedResult = null;

    public function __construct(
        private KrakenOrderSourceStoreLocationFactory $krakenOrderSourceStoreLocationFactory
    ) {

    }


    public function provide(): KrakenOrderSourceStoreLocation
    {
        if ($this->cachedResult) {
            return $this->cachedResult;
        }
        // TODO - adjust the logic
        /** @var KrakenOrderSourceStoreLocation $krakenOrderSourceStoreLocation */
        $krakenOrderSourceStoreLocation = $this->krakenOrderSourceStoreLocationFactory->create();
        $krakenOrderSourceStoreLocation
            ->setStoreId('1')
            ->setStoreName('shop')
            ->setWarehouseId('keh')
            ->setWarehouseName('KEH Warehouse');

        $this->cachedResult = $krakenOrderSourceStoreLocation;

        return $this->cachedResult;
    }
}
