<?php
declare(strict_types=1);

namespace Keh\NetSuite\Model\DataProvider\Kraken;

use Keh\NetSuite\Model\DataProvider\Kraken\OrderFulfillmentStage as KrakenOrderFulfillmentStage;
use Keh\NetSuite\Model\DataProvider\Kraken\OrderSourceSalesRep as KrakenOrderSalesRep;
use Keh\NetSuite\Model\DataProvider\Kraken\OrderSourceStoreLocation as KrakenOrderStoreLocation;
use Kraken\V1\SalesOrderFulfillment as KrakenOrderFulfillment;
use Kraken\V1\SalesOrderFulfillmentFactory as KrakenOrderFulfillmentFactory;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Model\Order;

readonly class OrderFulfillment
{
    public function __construct(
        private KrakenOrderFulfillmentFactory $krakenOrderFulfillmentFactory,
        private KrakenOrderSalesRep $krakenOrderSalesRep,
        private KrakenOrderStoreLocation $krakenOrderStoreLocation,
        private KrakenOrderFulfillmentStage $krakenOrderFulfillmentStage,
    ) {
    }

    public function provide(OrderInterface $magentoOrder): KrakenOrderFulfillment
    {
        /** @var KrakenOrderFulfillment $krakenOrderFulfillment */
        $krakenOrderFulfillment = $this->krakenOrderFulfillmentFactory->create();
        // TODO - adjust the logic
        $krakenOrderFulfillment
            ->setFulfillmentStatus(
                $magentoOrder->getStatus() === Order::STATE_COMPLETE ?
                    'shipped' :
                    'in_progress'
            )->setStages([$this->krakenOrderFulfillmentStage->provide($magentoOrder->getStatus())])
            ->setSalesRep($this->krakenOrderSalesRep->provide())
            ->setStoreLocation($this->krakenOrderStoreLocation->provide());

        return $krakenOrderFulfillment;
    }
}
