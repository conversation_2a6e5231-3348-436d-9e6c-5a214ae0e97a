<?php
declare(strict_types=1);

namespace Keh\NetSuite\Model\DataProvider\Kraken;

use Keh\NetSuite\Model\DataProvider\Kraken\OrderItemDiscount as KrakenOrderItemDiscount;
use <PERSON>raken\V1\SalesOrderItem as KrakenOrderItem;
use Kraken\V1\SalesOrderItemFactory as KrakenOrderItemFactory;
use Magento\Sales\Api\Data\OrderInterface;

readonly class OrderItem
{
    public function __construct(
        private KrakenOrderItemFactory $krakenOrderItemFactory,
        private KrakenOrderItemDiscount $krakenOrderItemDiscount,
    ) {

    }

    /**
     * @param OrderInterface $magentoOrder
     * @return KrakenOrderItem[]
     */
    public function provide(OrderInterface $magentoOrder): array
    {
        $result = [];
        foreach ($magentoOrder->getItems() as $item) {
            /** @var KrakenOrderItem $krakenOrderItem */
            $krakenOrderItem = $this->krakenOrderItemFactory->create();
            $krakenOrderItem
                ->setItemId($item->getItemId())
                ->setPrice($item->getPrice())
                ->setQuantity($item->getQtyOrdered())
                ->setSku($item->getSku())
                ->setItemDiscounts($this->krakenOrderItemDiscount->provide($item))
                ->setTotalPrice((float)$item->getRowTotalInclTax())
                ->setItemDiscountAmount((float)$item->getDiscountAmount())
                ->setPriceBeforeDiscount((float)$item->getPrice())
                ->setProductName((string)$item->getName())
                ->setTaxAmount((float)$item->getTaxAmount());

            $result[] = $krakenOrderItem;
        }

        return $result;
    }
}
