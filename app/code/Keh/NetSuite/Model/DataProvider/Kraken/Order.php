<?php
declare(strict_types=1);

namespace Keh\NetSuite\Model\DataProvider\Kraken;

use DateMalformedStringException;
use DateTime;
use Google\Protobuf\Timestamp;
use Google\Protobuf\TimestampFactory;
use Keh\Base\Model\Command\GetOrderByIncrementId;
use Keh\NetSuite\Model\DataProvider\Kraken\OrderCreditApplied as KrakenOrderCreditApplied;
use Keh\NetSuite\Model\DataProvider\Kraken\OrderCustomer as KrakenOrderCustomer;
use Keh\NetSuite\Model\DataProvider\Kraken\OrderDiscountCodes as KrakenOrderDiscountCodes;
use Keh\NetSuite\Model\DataProvider\Kraken\OrderFulfillment as KrakenOrderFulfillment;
use Keh\NetSuite\Model\DataProvider\Kraken\OrderGiftCards as KrakenOrderGiftCards;
use Keh\NetSuite\Model\DataProvider\Kraken\OrderItem as KrakenOrderItem;
use Keh\NetSuite\Model\DataProvider\Kraken\OrderPayment as KrakenOrderPayment;
use Keh\NetSuite\Model\DataProvider\Kraken\OrderShippingDetails as KrakenOrderShippingDetails;
use Keh\NetSuite\Model\DataProvider\Kraken\OrderSource as KrakenOrderSource;
use Kraken\V1\SalesOrder as KrakenOrder;
use Kraken\V1\SalesOrderFactory as KrakenOrderFactory;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Provide Kraken order class data based on Magento order.
 */
readonly class Order
{
    public function __construct(
        private KrakenOrderFactory $orderFactory,
        private GetOrderByIncrementId $getOrderByIncrementId,
        private TimestampFactory $googleTimestampFactory,
        private KrakenOrderSource $krakenOrderSource,
        private KrakenOrderFulfillment $krakenOrderFulfillment,
        private KrakenOrderDiscountCodes $krakenOrderDiscountCodes,
        private KrakenOrderCustomer $krakenOrderCustomer,
        private KrakenOrderItem $krakenOrderItem,
        private KrakenOrderShippingDetails $krakenOrderShippingDetails,
        private KrakenOrderPayment $krakenOrderPayment,
        private KrakenOrderGiftCards $krakenOrderGiftCards,
        private KrakenOrderCreditApplied $krakenOrderCreditApplied
    ) {

    }

    /**
     * @param int $orderIncrementId
     *
     * @return string
     *
     * @throws NoSuchEntityException
     * @throws DateMalformedStringException
     */
    public function provide(int $orderIncrementId): string
    {
        $magentoOrder = $this->getOrderByIncrementId->execute($orderIncrementId);
        if (!$magentoOrder) {
            throw new NoSuchEntityException(
                __('Order with incrementId does not exist, incrementId: %s', $orderIncrementId)
            );
        }
        /** @var Timestamp $createdAt */
        $createdAt = $this->googleTimestampFactory->create();
        $createdAt->fromDateTime(new DateTime($magentoOrder->getCreatedAt()));
        /** @var Timestamp $updatedAt */
        $updatedAt = $this->googleTimestampFactory->create();
        $updatedAt->fromDateTime(new DateTime($magentoOrder->getUpdatedAt()));

        /** @var KrakenOrder $krakenOrder */
        $krakenOrder = $this->orderFactory->create();
        $krakenOrder
            ->setOrderId((string)$orderIncrementId)
            ->setOrderStatus((string)$magentoOrder->getStatus())
            ->setOrderType('ONLINE') //TODO create order type, ex: ['ONLINE', 'IN_STORE', 'PHONE', 'THIRD_PARTY'];
            ->setSubtotalAmount((float)$magentoOrder->getSubtotal())
            ->setTaxAmount((float)$magentoOrder->getTaxAmount())
            ->setShippingAmount((float)$magentoOrder->getShippingAmount())
            ->setOrderDiscountAmount((float)$magentoOrder->getDiscountAmount())
            ->setTotalAmount((float)$magentoOrder->getGrandTotal())
            ->setOrderNotes((string)$magentoOrder->getShippingDescription())
            ->setOrderSource($this->krakenOrderSource->provide($orderIncrementId))
            ->setOrderFulfillment($this->krakenOrderFulfillment->provide($magentoOrder))
            ->setDiscountCodes($this->krakenOrderDiscountCodes->provide($magentoOrder))
            ->setCustomer($this->krakenOrderCustomer->provide($magentoOrder))
            ->setItems($this->krakenOrderItem->provide($magentoOrder))
            ->setPayments([$this->krakenOrderPayment->provide($magentoOrder)])
            ->setGiftCards($this->krakenOrderGiftCards->provide($magentoOrder))
            ->setCreditApplied($this->krakenOrderCreditApplied->provide($magentoOrder))
//            ->setRefunds() //TODO implement once we has refunds.
            ->setOrderDate($createdAt)
            ->setCreatedAt($createdAt)
            ->setUpdatedAt($updatedAt);
        if ($magentoOrder->getShippingAddress()) {
            $krakenOrder->setShippingDetails($this->krakenOrderShippingDetails->provide($magentoOrder));
        }

        return $krakenOrder->serializeToJsonString();
    }
}
