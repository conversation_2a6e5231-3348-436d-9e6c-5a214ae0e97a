<?php
declare(strict_types=1);

namespace Keh\NetSuite\Model\DataProvider\Kraken;

use Kraken\V1\SalesOrderShippingDetails as KrakenOrderShippingDetails;
use Kraken\V1\SalesOrderShippingDetailsFactory as KrakenOrderShippingDetailsFactory;
use Magento\Sales\Api\Data\OrderInterface;

readonly class OrderShippingDetails
{
    public function __construct(
        private KrakenOrderShippingDetailsFactory $krakenOrderShippingDetailsFactory
    ) {

    }

    public function provide(OrderInterface $magentoOrder): KrakenOrderShippingDetails
    {
        /** @var KrakenOrderShippingDetails $krakenOrderShippingDetails */
        $krakenOrderShippingDetails = $this->krakenOrderShippingDetailsFactory->create();
        $krakenOrderShippingDetails
            ->setCarrier('fedex')
            ->setShippingMethod((string)$magentoOrder->getShippingMethod())
//            ->setTrackingNumber()
//            ->setShippingInsurance()
//            ->setExpectedDeliveryDate() //TODO implement once we have this data
            ->setSignatureRequired(false);

        return $krakenOrderShippingDetails;
    }
}
