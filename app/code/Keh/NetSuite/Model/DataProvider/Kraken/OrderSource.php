<?php
declare(strict_types=1);

namespace Keh\NetSuite\Model\DataProvider\Kraken;

use Kraken\V1\SalesOrderSource as KrakenOrderSource;
use Kraken\V1\SalesOrderSourceFactory as KrakenOrderSourceFactory;

readonly class OrderSource
{
    public function __construct(
        private KrakenOrderSourceFactory $krakenOrderSourceFactory,
        private OrderSourceStoreLocation $orderSourceStoreLocation,
        private OrderSourceSalesRep $orderSourceSalesRep,
    ) {
    }

    public function provide(int $orderIncrementId): KrakenOrderSource
    {
        /** @var KrakenOrderSource $orderSource */
        $orderSource = $this->krakenOrderSourceFactory->create();
        $orderSource
            ->setSourceName('ONLINE') //TODO create order type, ex: ['ONLINE', 'IN_STORE', 'PHONE', 'THIRD_PARTY'];
            ->setSourceReferenceId((string)$orderIncrementId)
            ->setChannel('website')
            ->setStoreLocation($this->orderSourceStoreLocation->provide())
            ->setSalesRep($this->orderSourceSalesRep->provide());

        return $orderSource;
    }
}
