<?php
declare(strict_types=1);

namespace Keh\NetSuite\Model\DataProvider\Kraken;

use Kraken\V1\SalesOrderAddress as KrakenOrderAddress;
use Kraken\V1\SalesOrderAddressFactory as KrakenOrderAddressFactory;
use Magento\Sales\Api\Data\OrderAddressInterface;

readonly class OrderAddress
{
    public function __construct(
        private KrakenOrderAddressFactory $krakenOrderAddressFactory,
    ) {

    }

    public function provide(OrderAddressInterface $orderAddress): KrakenOrderAddress
    {
        /** @var KrakenOrderAddress $krakenOrderAddress */
        $krakenOrderAddress = $this->krakenOrderAddressFactory->create();
        $krakenOrderAddress
            ->setAddressLine1((string)$orderAddress->getStreetLine(1))
            ->setAddressLine2((string)$orderAddress->getStreetLine(2))
            ->setCity((string)$orderAddress->getCity())
            ->setState((string)$orderAddress->getRegion())
            ->setCountry((string)$orderAddress->getCountryId())
            ->setPostalCode((string)$orderAddress->getPostcode());

        return $krakenOrderAddress;
    }
}
