<?php
declare(strict_types=1);

namespace Keh\NetSuite\Model\DataProvider\Kraken;

use Kraken\V1\SalesOrderPayment as KrakenOrderPayment;
use Kraken\V1\SalesOrderPaymentFactory as KrakenOrderPaymentFactory;
use Magento\Sales\Api\Data\OrderInterface;

readonly class OrderPayment
{
    public function __construct(
        private KrakenOrderPaymentFactory $krakenOrderPaymentFactory
    ) {

    }

    public function provide(OrderInterface $magentoOrder): KrakenOrderPayment
    {
        $magentoOrderPayment = $magentoOrder->getPayment();

        /** @var KrakenOrderPayment $krakenOrderPayment */
        $krakenOrderPayment = $this->krakenOrderPaymentFactory->create();
        $krakenOrderPayment
            ->setPaymentAmount((float)$magentoOrderPayment->getAmountPaid())
            ->setAuthCode('')
            ->setAvsResult('')
            ->setCardType((string)$magentoOrderPayment->getCcType())
            ->setCvvResult('')
            ->setExpirationDate(
                sprintf('%s/%s', $magentoOrderPayment->getCcExpMonth(), $magentoOrderPayment->getCcExpYear())
            )->setFraudDetectionScore('0') //TODO implemet from Signify once we do that
            ->setLast4((string)$magentoOrderPayment->getCcLast4())
            ->setPaymentGateway('')
            ->setPaymentMethod((string)$magentoOrderPayment->getMethod())
            ->setPaymentToken('')
            ->setTransactionId((string)$magentoOrderPayment->getCcTransId())
            ->setPaymentStatus('captured'); // TODO change once we change payment mode

        return $krakenOrderPayment;
    }
}
