<?php
declare(strict_types=1);

namespace Keh\NetSuite\Model\DataProvider\Kraken;

use Kraken\V1\SalesOrderSourceSalesRepFactory as KrakenOrderSourceSalesRepFactory;
use <PERSON>raken\V1\SalesOrderSourceSalesRep as KrakenOrderSourceSalesRep;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class OrderSourceSalesRep
{
    private ?KrakenOrderSourceSalesRep $cachedResult = null;

    public function __construct(
        private readonly KrakenOrderSourceSalesRepFactory $orderSourceSalesRepFactory,
        private readonly ScopeConfigInterface $scopeConfig
    ) {

    }

    public function provide(): KrakenOrderSourceSalesRep
    {
        if ($this->cachedResult) {
            return $this->cachedResult;
        }
        /** @var KrakenOrderSourceSalesRep $orderSourceSalesRep */
        $orderSourceSalesRep = $this->orderSourceSalesRepFactory->create();
        $orderSourceSalesRep
            ->setSalesRepId('1')
            ->setSalesRepName(
                (string)$this->scopeConfig->getValue('trans_email/ident_sales/name', ScopeInterface::SCOPE_STORE, 1)
            )->setSalesRepEmail(
                (string)$this->scopeConfig->getValue('trans_email/ident_sales/email', ScopeInterface::SCOPE_STORE, 1)
            );

        return $this->cachedResult = $orderSourceSalesRep;
    }
}
