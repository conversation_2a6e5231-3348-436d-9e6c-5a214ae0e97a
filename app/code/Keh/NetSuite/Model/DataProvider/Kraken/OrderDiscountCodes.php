<?php
declare(strict_types=1);

namespace Keh\NetSuite\Model\DataProvider\Kraken;

use Kraken\V1\SalesOrderDiscountCodes as KrakenOrderDiscountCodes;
use Kraken\V1\SalesOrderDiscountCodesFactory as KrakenOrderDiscountCodesFactory;
use Magento\Sales\Api\Data\OrderInterface;

readonly class OrderDiscountCodes
{
    public function __construct(
        private KrakenOrderDiscountCodesFactory $krakenOrderDiscountCodesFactory,
    ) {

    }

    /**
     * @param OrderInterface $magentoOrder
     * @return KrakenOrderDiscountCodes[]
     */
    public function provide(OrderInterface $magentoOrder): array
    {
        $result = [];
        if ($magentoOrder->getCouponCode()) {
            /** @var KrakenOrderDiscountCodes $krakenOrderDiscountCode */
            $krakenOrderDiscountCode = $this->krakenOrderDiscountCodesFactory->create();
            $krakenOrderDiscountCode
                ->setDiscountCode($magentoOrder->getCouponCode())
                ->setDiscountAmount($magentoOrder->getDiscountAmount()) //TODO, need check if not mixed with others
                ->setDiscountType('magento_coupon');

            $result[] = $krakenOrderDiscountCode;
        }

        return $result;
    }
}
