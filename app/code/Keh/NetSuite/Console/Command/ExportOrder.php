<?php
declare(strict_types=1);

namespace Keh\NetSuite\Console\Command;

use Keh\NetSuite\Api\Data\QueueCommunicatorInterfaceFactory;
use Keh\NetSuite\Api\Data\QueueCommunicatorInterface;
use Keh\NetSuite\Model\Queue\Publisher;
use Magento\Framework\Console\Cli;
use Magento\Framework\Serialize\Serializer\Json;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ExportOrder extends Command
{
    private const string ORDER_ID = 'order_id';

    public function __construct(
        private readonly QueueCommunicatorInterfaceFactory $queueCommunicatorFactory,
        private readonly Publisher $queuePublisher,
        private readonly Json $json,
        ?string $name = null
    ) {
        parent::__construct($name);
    }

    /**
     * Initialization of the command.
     */
    protected function configure()
    {
        $this->setName('keh:netsuite:export_order');
        $this->setDescription('Sends order to RabbitMQ queue to export to kafka');
        $this->addOption(self::ORDER_ID, null, InputOption::VALUE_REQUIRED, 'Order Increment ID');

        parent::configure();
    }

    /**
     * CLI command description.
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     *
     * @return void
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $orderId = $input->getOption(self::ORDER_ID);

        /** @var QueueCommunicatorInterface $data */
        $data = $this->queueCommunicatorFactory->create();
        $data->setEventName(QueueCommunicatorInterface::EVENT_ORDER_PLACED)
            ->setEventData($this->json->serialize(
                [
                    'increment_id' => (int)$orderId
                ]
            ));

        $this->queuePublisher->execute($data);

        return Cli::RETURN_SUCCESS;
    }
}
