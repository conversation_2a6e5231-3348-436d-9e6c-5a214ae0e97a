<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Keh\NetSuite\Api\Data\QueueCommunicatorInterface" type="Keh\NetSuite\Model\Data\QueueCommunicator"/>

    <!-- Logger -->
    <virtualType name="NetSuiteLogHandler" type="Magento\Framework\Logger\Handler\Base">
        <arguments>
            <argument name="fileName" xsi:type="string">/var/log/netsuite.log</argument>
        </arguments>
    </virtualType>
    <type name="Keh\NetSuite\Logger\Logger">
        <arguments>
            <argument name="name" xsi:type="string">NetSuiteLog</argument>
            <argument name="handlers" xsi:type="array">
                <item name="debug" xsi:type="object">NetSuiteLogHandler</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="keh_netsuite_export_order" xsi:type="object">Keh\NetSuite\Console\Command\ExportOrder</item>
            </argument>
        </arguments>
    </type>
</config>
