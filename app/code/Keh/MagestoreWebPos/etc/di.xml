<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="sales_order_grid_data_source" xsi:type="string">Keh\MagestoreWebPos\Model\ResourceModel\Order\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <type name="Magestore\Webpos\Model\Shipping\AbstractMethod">
        <plugin name="override_tracking_available" type="Keh\MagestoreWebPos\Plugin\Shipping\AbstractMethodPlugin" sortOrder="10"/>
    </type>
</config>
