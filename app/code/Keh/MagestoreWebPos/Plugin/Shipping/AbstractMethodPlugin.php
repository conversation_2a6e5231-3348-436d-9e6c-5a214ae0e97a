<?php
/**
 * Plugin to override isTrackingAvailable method
 */

namespace Keh\MagestoreWebPos\Plugin\Shipping;

use Magestore\Webpos\Model\Shipping\AbstractMethod;

class AbstractMethodPlugin
{
    /**
     * Override isTrackingAvailable to return false, as we don't need it
     *
     * @param AbstractMethod $subject
     * @param bool $result
     * @return bool
     */
    public function afterIsTrackingAvailable(AbstractMethod $subject, $result)
    {
        return false;
    }
}
