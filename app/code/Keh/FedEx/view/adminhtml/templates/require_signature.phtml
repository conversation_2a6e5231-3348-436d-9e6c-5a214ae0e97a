<?php
/** @var \Magento\Sales\Block\Adminhtml\Order\Create\Form $block */
/** @var \Magento\Framework\Escaper $escaper */

/** @var \Keh\FedEx\ViewModel\RequireSignature $viewModel */
$viewModel = $block->getData('view_model');

$configProvider = $viewModel->getConfig();

$signatureRequired = $block->getQuote()->getGrandTotal() > $configProvider->getSignatureRequiredThreshold();

$defaultOption = $signatureRequired
    ? $configProvider->getSignatureRequiredOption()
    : $configProvider->getNoSignatureRequiredOption();
?>
<section class="admin__page-subsection" style="margin-top: 4rem;">
    <div class="admin__page-section-title">
        <span class="title">
              <?php echo __('Require Signature'); ?>
        </span>
    </div>
    <div class="admin__page-section-item-content edit-require_signature" id="edit-require_signature-form" >
        <fieldset class="admin__fieldset">
            <div class="admin__field">
                <label for="extension_attribute.require_signature" class="admin__field-label">
                    <span><?= $block->escapeHtml(__('Require Signature')); ?></span>
                </label>
                <div class="control admin__field-control">
                    <select class="select admin__control-select" name="order[extension_attributes][require_signature][require_signature]" id="require_signature">
                        <?php foreach ($viewModel->getRequireSignatureOptions() as $key => $value) : ?>
                            <option value="<?php echo $escaper->escapeHtmlAttr($key);?>"
                                <?php if ($key == $defaultOption): ?>selected="selected"<?php endif; ?>
                            >
                                <?php echo $escaper->escapeHtmlAttr($value);?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            <div class="admin__field">
                <label for="require_signature_reason" class="admin__field-label">
                    <span><?= $block->escapeHtml(__('Reason')) ?></span>
                </label>
                <div class="control admin__field-control">
                    <input type="text" class="input-text admin__control-text" id="require_signature_reason" value=""
                           name="order[extension_attributes][require_signature][require_signature_reason]"/>
                    <input type="hidden" id="grand_total" value="<?php echo $block->getQuote()->getGrandTotal();?>"/>
                </div>
            </div>
        </fieldset>
    </div>
</section>
<script>
    const requireSignature = document.getElementById('require_signature');
    const requireSignatureReason = document.getElementById('require_signature_reason');
    const grandTotal = document.getElementById('grand_total');

    requireSignature.addEventListener('change', (event) => {
        if (event.target.value === '<?php echo $escaper->escapeJs($configProvider->getNoSignatureRequiredOption());?>'
            && grandTotal.value > <?php echo $escaper->escapeJs($configProvider->getSignatureRequiredThreshold());?>

        ) {
            requireSignatureReason.classList.add('required-entry');
            requireSignatureReason.parentNode.parentNode.classList.add('required')
            requireSignatureReason.parentNode.parentNode.classList.add('_required')
        } else {
            requireSignatureReason.classList.remove('required-entry');
            requireSignatureReason.parentNode.parentNode.classList.remove('required');
            requireSignatureReason.parentNode.parentNode.classList.remove('_required');

       }
    });
</script>
