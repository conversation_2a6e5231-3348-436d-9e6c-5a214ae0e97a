<?php
/** @var \Magento\Sales\Block\Adminhtml\Order\View\Tab\Info $block */

/** @var \Magento\Framework\Escaper $escaper */

$order = $block->getOrder();
$requireSignature = $order?->getExtensionAttributes()?->getRequireSignature();
/** @var \Keh\FedEx\ViewModel\RequireSignature $viewModel */
$viewModel = $block->getData('view_model');
?>
<?php if ($requireSignature): ?>
    <section class="admin__page-subsection" style="margin-top: 4rem;">
        <div class="admin__page-section-title">
        <span class="title">
              <?php echo __('Require Signature'); ?>
        </span>
        </div>
        <div class="admin__page-section-item-content edit-require_signature" id="edit-require_signature-form">
            <p>
                <strong><?php echo __('Require Signature'); ?>:</strong>
                <span><?php echo $viewModel->getRequireSignatureOptions()[$requireSignature->getRequireSignature()] ?: ''; ?></span>
            </p>
            <p>
                <strong><?php echo __('Reason'); ?></strong>
                <span><?php echo $requireSignature->getRequireSignatureReason(); ?></span>
            </p>
            <p>
                <strong><?php echo __('Admin'); ?></strong>
                <span><?php echo $requireSignature->getAdminUsername(); ?></span>
            </p>
        </div>
    </section>
<?php endif; ?>
