<?php
declare(strict_types=1);

namespace Keh\FedEx\Plugin;

use Keh\FedEx\Model\RequireSignatureRepository;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Sales\Api\Data\OrderExtensionFactory;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\OrderSearchResultInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Psr\Log\LoggerInterface;

readonly class OrderRepositoryAddRequireSignatureAttributePlugin
{
    /**
     * OrderRepositoryAddExtensionAttributePlugin constructor
     *
     * @param RequireSignatureRepository $repository
     * @param OrderExtensionFactory $orderExtensionFactory
     * @param LoggerInterface $logger
     */
    public function __construct(
        private RequireSignatureRepository $repository,
        private OrderExtensionFactory $orderExtensionFactory,
        private LoggerInterface $logger
    ) {

    }

    public function afterGet(OrderRepositoryInterface $subject, OrderInterface $order): OrderInterface
    {
        return $this->addRequireSignatureToOrder($order);
    }

    public function afterGetList(OrderRepositoryInterface $subject, OrderSearchResultInterface $result)
    {
        foreach ($result->getItems() as $order) {
            $this->addRequireSignatureToOrder($order);
        }

        return $result;
    }

    private function addRequireSignatureToOrder(OrderInterface &$order): OrderInterface
    {
        try {
            $requireSignature = $this->repository->getByOrderId((int)$order->getId());
        } catch (NoSuchEntityException $e) {
            $this->logger->error($e->getMessage(), ['exception' => $e, 'trace' => $e->getTrace()]);

            return $order;
        }

        if (!$requireSignature->getId()) {
            return $order;
        }

        $extensionAttributes = $order->getExtensionAttributes()
            ? $order->getExtensionAttributes()
            : $this->orderExtensionFactory->create();

        $extensionAttributes->setRequireSignature($requireSignature);
        $order->setExtensionAttributes($extensionAttributes);

        return $order;
    }

}
