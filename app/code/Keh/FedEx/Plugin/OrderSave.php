<?php
declare(strict_types=1);

namespace Keh\FedEx\Plugin;

use Exception;
use Keh\FedEx\Api\Data\RequireSignatureInterface;
use Keh\FedEx\Api\RequireSignatureRepositoryInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;
use Psr\Log\LoggerInterface;

readonly class OrderSave
{
    public function __construct(
        private RequireSignatureRepositoryInterface $requireSignatureRepository,
        private LoggerInterface $logger
    ) {

    }

    public function afterSave(
        OrderRepositoryInterface $subject,
        OrderInterface $order
    ) {
        $extensionAttributes = $order->getExtensionAttributes();
        /** @var RequireSignatureInterface $requireSignature */

        $requireSignature = $extensionAttributes?->getRequireSignature();

        try {
            if ($requireSignature) {
                $requireSignature->setOrderId($order->getId());
                $this->requireSignatureRepository->save($requireSignature);
            }
        } catch (Exception $e) {
            $this->logger->error($e->getMessage(), ['exception' => $e->getMessage(), 'trace' => $e->getTrace()]);
        }

        return $order;
    }
}
