<?php
declare(strict_types=1);

namespace Keh\FedEx\Observer;

use Exception;
use Keh\FedEx\Api\Data\RequireSignatureInterfaceFactory;
use Keh\FedEx\Model\RequireSignatureRepository;
use Magento\Backend\Model\Auth\Session;
use Magento\Framework\App\Request\Http;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Psr\Log\LoggerInterface;

readonly class OrderSaveRequiredSignature implements ObserverInterface
{
    public function __construct(
        private Http $request,
        private RequireSignatureInterfaceFactory $requireSignatureFactory,
        private RequireSignatureRepository $requireSignatureRepository,
        private Session $authSession,
        private LoggerInterface $logger
    ) {

    }
    public function execute(Observer $observer)
    {
        /** @var OrderInterface $order */
        $order = $observer->getEvent()->getData('order');

        $orderData = $this->request->getPost('order');

        if (array_key_exists('extension_attributes', $orderData)
            && array_key_exists('require_signature', $orderData['extension_attributes'])
        ) {
            $requireSignatureData = $orderData['extension_attributes']['require_signature'];

            $requireSignature = $this->requireSignatureFactory->create()
                ->setOrderId((int)$order->getEntityId())
                ->setAdminId((int)$this->authSession->getUser()->getId())
                ->setRequireSignature($requireSignatureData['require_signature'])
                ->setRequireSignatureReason($requireSignatureData['require_signature_reason']);

            $extensionAttributes = $order->getExtensionAttributes();
            $extensionAttributes->setRequireSignature($requireSignature);

            try {
                $this->requireSignatureRepository->save($requireSignature);
                $order->setExtensionAttributes($extensionAttributes);
            } catch (Exception $e) {
                $this->logger->error($e->getMessage(), ['exception' => $e, 'trace' => $e->getTrace()]);
            }
        }
    }
}
