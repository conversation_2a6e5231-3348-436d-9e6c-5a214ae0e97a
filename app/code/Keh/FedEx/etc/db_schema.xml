<?xml version="1.0" encoding="UTF-8" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="keh_fedex_require_signature" resource="default" engine="innodb" comment="Fedex Shipping Require Signature">
        <column xsi:type="int" name="require_signature_id" unsigned="false" nullable="false" identity="true" comment="Require Signature ID"/>
        <column xsi:type="int" name="order_id" unsigned="true" nullable="false" identity="false" default="0" comment="Order ID"/>
        <column xsi:type="int" name="admin_id" unsigned="true" nullable="false" identity="false" default="0" comment="Admin ID"/>
        <column xsi:type="varchar" name="require_signature" length="256" nullable="false" default="0" comment="Require signature"/>
        <column xsi:type="varchar" name="require_signature_reason" length="256" nullable="false" default="0" comment="Require signature reason"/>

        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="require_signature_id"/>
        </constraint>

        <constraint xsi:type="foreign" referenceId="KEH_FEDEX_REQUIRE_SIGNATURE_ORDER_ID" table="keh_fedex_require_signature" column="order_id" referenceTable="sales_order" referenceColumn="entity_id" onDelete="CASCADE"/>
        <constraint xsi:type="foreign" referenceId="KEH_FEDEX_REQUIRE_SIGNATURE_ADMIN_ID" table="keh_fedex_require_signature" column="admin_id" referenceTable="admin_user" referenceColumn="user_id" onDelete="NO ACTION"/>

        <index referenceId="KEH_FEDEX_REQUIRE_SIGNATURE_ADMIN_ID" indexType="btree">
            <column name="admin_id"/>
        </index>
    </table>
</schema>
