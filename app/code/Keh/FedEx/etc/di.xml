<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\Fedex\Model\Carrier" type="Keh\FedEx\Model\Rewrite\Magento\Fedex\Model\Carrier" />
    <preference for="Keh\FedEx\Api\Data\RequireSignatureInterface" type="Keh\FedEx\Model\RequireSignature"/>
    <preference for="Keh\FedEx\Api\RequireSignatureRepositoryInterface" type="Keh\FedEx\Model\RequireSignatureRepository"/>

    <type name="Magento\Sales\Api\OrderRepositoryInterface">
        <plugin name="get_fedex_required_signature_attribute" type="Keh\FedEx\Plugin\OrderRepositoryAddRequireSignatureAttributePlugin" />
        <plugin name="save_fedex_required_signature_attribute" type="Keh\FedEx\Plugin\OrderSave"/>
    </type>
</config>
