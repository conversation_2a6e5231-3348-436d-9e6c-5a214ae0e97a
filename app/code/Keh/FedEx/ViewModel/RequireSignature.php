<?php
declare(strict_types=1);

namespace Keh\FedEx\ViewModel;

use Keh\FedEx\Model\ConfigProvider;

readonly class RequireSignature implements \Magento\Framework\View\Element\Block\ArgumentInterface
{
    public function __construct(
        private \Keh\FedEx\Model\Config\Source\SignatureRequired $signatureRequired,
        private \Keh\FedEx\Model\ConfigProvider $configProvider,
    ) {

    }

    public function getRequireSignatureOptions(): array
    {
        return $this->signatureRequired->toOptionArray();
    }

    public function getConfig(): ConfigProvider
    {
        return $this->configProvider;
    }
}
