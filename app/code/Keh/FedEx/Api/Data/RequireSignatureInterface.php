<?php
declare(strict_types=1);

namespace Keh\FedEx\Api\Data;

use Magento\Framework\Api\ExtensibleDataInterface;

interface RequireSignatureInterface extends ExtensibleDataInterface
{
    public const string ORDER_ID = 'order_id';
    public const string ADMIN_ID = 'admin_id';
    public const string REQUIRE_SIGNATURE = 'require_signature';
    public const string REQUIRE_SIGNATURE_REASON = 'require_signature_reason';

    /**
     * @return int
     */
    public function getOrderId(): int;

    /**
     * @param int $orderId
     * @return RequireSignatureInterface
     */
    public function setOrderId(int $orderId): RequireSignatureInterface;

    /**
     * @return int
     */
    public function getAdminId(): int;

    /**
     * @return string
     */
    public function getAdminUsername(): string;

    /**
     * @param int $adminId
     * @return RequireSignatureInterface
     */
    public function setAdminId(int $adminId): RequireSignatureInterface;

    /**
     * @return int
     */
    public function getRequireSignature(): string;

    /**
     * @param string $requireSignature
     * @return RequireSignatureInterface
     */
    public function setRequireSignature(string $requireSignature): RequireSignatureInterface;

    /**
     * @return string
     */
    public function getRequireSignatureReason(): string;

    /**
     * @param string $reason
     * @return RequireSignatureInterface
     */
    public function setRequireSignatureReason(string $reason): RequireSignatureInterface;
}
