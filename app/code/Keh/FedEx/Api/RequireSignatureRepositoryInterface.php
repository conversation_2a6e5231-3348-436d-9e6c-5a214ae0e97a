<?php
declare(strict_types=1);

namespace Keh\FedEx\Api;

use Keh\FedEx\Api\Data\RequireSignatureInterface;
use Magento\Framework\Exception\LocalizedException;

interface RequireSignatureRepositoryInterface
{
    /**
     * @param RequireSignatureInterface $requireSignature
     *
     * @return bool
     * @throws LocalizedException
     *
     */
    public function save(RequireSignatureInterface $requireSignature): bool;

    /**
     * @param int $orderId
     *
     * @return RequireSignatureInterface
     */
    public function getByOrderId(int $orderId): RequireSignatureInterface;
}
