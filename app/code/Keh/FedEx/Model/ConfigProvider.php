<?php
declare(strict_types=1);

namespace Keh\FedEx\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

readonly class ConfigProvider
{
    private const string KEH_FEDEX_RATES_KEH_RATES = 'keh_fedex/rates/keh_rates';
    private const string KEH_FEDEX_REQUIRE_SIGNATURE_NO_SIGNATURE = 'keh_fedex/require_signature/no_signature';
    private const string KEH_FEDEX_REQUIRE_SIGNATURE_SIGNATURE_REQUIRED = 'keh_fedex/require_signature/signature_required';
    private const string KEH_FEDEX_REQUIRE_SIGNATURE_THRESHOLD = 'keh_fedex/require_signature/threshold';

    /**
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        private ScopeConfigInterface $scopeConfig,
    ) {

    }

    /**
     * @param int|null $storeId
     *
     * @return bool
     */
    public function getKehNegotiatedRates(?int $storeId = null): bool
    {
        return $this->scopeConfig->isSetFlag(
            self::KEH_FEDEX_RATES_KEH_RATES,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param string|null $storeId
     *
     * @return string
     */
    public function getNoSignatureRequiredOption(?string $storeId = null): string
    {
        return $this->scopeConfig->getValue(
            self::KEH_FEDEX_REQUIRE_SIGNATURE_NO_SIGNATURE,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param string|null $storeId
     *
     * @return string
     */
    public function getSignatureRequiredOption(?string $storeId = null): string
    {
        return $this->scopeConfig->getValue(
            self::KEH_FEDEX_REQUIRE_SIGNATURE_SIGNATURE_REQUIRED,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }

    /**
     * @param string|null $storeId
     *
     * @return float
     */
    public function getSignatureRequiredThreshold(?string $storeId = null): float
    {
        return (float)$this->scopeConfig->getValue(
            self::KEH_FEDEX_REQUIRE_SIGNATURE_THRESHOLD,
            ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
