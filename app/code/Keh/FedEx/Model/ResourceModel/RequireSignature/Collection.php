<?php
declare(strict_types=1);

namespace Keh\FedEx\Model\ResourceModel\RequireSignature;

use Keh\FedEx\Model\ResourceModel\RequireSignature as RequireSignatureResource;
use Keh\FedEx\Model\RequireSignature;
use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{
    protected function _construct()
    {
        $this->_init(RequireSignature::class, RequireSignatureResource::class);
    }
}
