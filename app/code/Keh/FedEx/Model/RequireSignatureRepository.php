<?php
declare(strict_types=1);

namespace Keh\FedEx\Model;
use Keh\FedEx\Api\Data\RequireSignatureInterface;
use Keh\FedEx\Api\RequireSignatureRepositoryInterface;

readonly class RequireSignatureRepository implements RequireSignatureRepositoryInterface
{
    public function __construct(
        private \Keh\FedEx\Model\ResourceModel\RequireSignature $resource,
        private \Keh\FedEx\Model\RequireSignatureFactory $factory
    ) {

    }

    /**
     * @inheritDoc
     */
    public function save(RequireSignatureInterface $requireSignature): bool
    {
        try {
            $this->resource->save($requireSignature);

        } catch (\Exception $exception) {
            return false;
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function getByOrderId(int $orderId): RequireSignatureInterface
    {
        $requireSignature = $this->factory->create();

        $this->resource->load($requireSignature, $orderId, 'order_id');

        return $requireSignature;
    }
}
