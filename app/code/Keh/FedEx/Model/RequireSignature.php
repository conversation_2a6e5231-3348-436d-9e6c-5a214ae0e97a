<?php
declare(strict_types=1);

namespace Keh\FedEx\Model;

use Keh\FedEx\Api\Data\RequireSignatureInterface;
use Magento\Framework\Model\AbstractModel;

class RequireSignature extends AbstractModel implements RequireSignatureInterface
{
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        private \Magento\User\Model\UserFactory $userFactory,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
    }

    protected function _construct()
    {
        $this->_init(ResourceModel\RequireSignature::class);
    }

    /**
     * @inheritDoc
     */
    public function getOrderId(): int
    {
        return (int)$this->getData(self::ORDER_ID);
    }

    /**
     * @inheritDoc
     */
    public function setOrderId(int $orderId): RequireSignatureInterface
    {
        $this->setData(self::ORDER_ID, $orderId);

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getAdminId(): int
    {
        return (int)$this->getData(self::ADMIN_ID);
    }

    public function getAdminUsername(): string
    {
        return $this->userFactory->create()->load($this->getAdminId())->getUsername();
    }

    /**
     * @inheritDoc
     */
    public function setAdminId(int $adminId): RequireSignatureInterface
    {
        $this->setData(self::ADMIN_ID, $adminId);

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getRequireSignature(): string
    {
        return $this->getData(self::REQUIRE_SIGNATURE);
    }

    /**
     * @inheritDoc
     */
    public function setRequireSignature(string $requireSignature): RequireSignatureInterface
    {
        $this->setData(self::REQUIRE_SIGNATURE, $requireSignature);

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function getRequireSignatureReason(): string
    {
        return $this->getData(self::REQUIRE_SIGNATURE_REASON);
    }

    /**
     * @inheritDoc
     */
    public function setRequireSignatureReason(string $reason): RequireSignatureInterface
    {
        $this->setData(self::REQUIRE_SIGNATURE_REASON, $reason);

        return $this;
    }
}
