<?php
declare(strict_types=1);

namespace Keh\FedEx\Model\Config\Source;
use Magento\Fedex\Model\Carrier;
use Magento\Framework\Data\OptionSourceInterface;

readonly class SignatureRequired implements OptionSourceInterface
{
    public function __construct(
        private Carrier $fedexCarrier
    ) {

    }

    /**
     * @inheritDoc
     */
    public function toOptionArray()
    {
        return $this->fedexCarrier->getDeliveryConfirmationTypes();
    }
}
