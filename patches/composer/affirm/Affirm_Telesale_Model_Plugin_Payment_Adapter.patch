diff --git a/vendor/affirm/magento2-telesales/Model/Plugin/Payment/Adapter.php b/vendor/affirm/magento2-telesales/Model/Plugin/Payment/Adapter.php
index 223ed8ecd..2751dfdf3 100644333
--- a/vendor/affirm/magento2-telesales/Model/Plugin/Payment/Adapter.php
+++ b/vendor/affirm/magento2-telesales/Model/Plugin/Payment/Adapter.php
@@ -43,7 +43,13 @@
     public function afterGetConfigPaymentAction(PaymentAdapter $subject, $result)
     {
         $info = $subject->getInfoInstance();
+        if (!$info) {
+            return $result;
+        }
         $_order = $info->getOrder();
+        if (!$_order) {
+            return $result;
+        }
         $paymentMethod = $_order->getPayment()->getMethod();
         $eventPrefix = $info->getEventPrefix();
