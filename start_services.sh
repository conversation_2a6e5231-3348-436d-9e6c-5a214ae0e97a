#!/bin/bash
#/build_magento.sh

# Start the first process
php-fpm -D
status=$?
#fpmpid=$(pidof php-fpm)
if [ $status -ne 0 ]; then
  echo "Failed to start php-fpm: $status"
  exit $status
fi


service nginx restart
# Start the second process
#apache2ctl -D FOREGROUND
#status=$?
#if [ $status -ne 0 ]; then
#  echo "Failed to start Apache2: $status"
#  exit $status
#fi

# Naive check runs checks once a minute to see if either of the processes exited.
# This illustrates part of the heavy lifting you need to do if you want to run
# more than one service in a container. The container exits with an error
# if it detects that either of the processes has exited.
# Otherwise it loops forever, waking up every 60 seconds

while sleep 5; do
	# Cheap trick to register the loss of the maintenance flag
	# must delete maintenance flag from $mountSrc not through CLI
	#ln -s $mountSrc/maintenance/.maintenance.flag $mountDest/var/.maintenance.flag
	#ln -s $mountSrc/maintenance/.maintenance.ip $mountDest/var/.maintenance.ip
	#ln -s $mountSrc/maintenance/.maintenance.fpc.state $mountDest/var/.maintenance.fpc.state

	#ps aux | grep apache2 | grep -q -v grep
	#PROCESS_1_STATUS=$?
	ps aux | grep php-fpm | grep -q -v grep
	PROCESS_2_STATUS=$?

	# If the greps above find anything, they exit with 0 status
	# If they are not both 0, then something is wrong
	#if [ $PROCESS_1_STATUS -ne 0 -o $PROCESS_2_STATUS -ne 0 ]; then
	#if [ $PROCESS_2_STATUS -ne 0 ]; then
	#	echo "One of the processes has already exited."
	#	exit 1
	#fi
done
