<?php
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Magento\Update\Queue;

class AbstractJobTest extends \PHPUnit_Framework_TestCase
{
    public function testToString()
    {
        /** Any implementation of abstract job can be used for __toString testing */
        $job = new \Magento\Update\Queue\JobBackup(
            'backup',
            ['targetArchivePath' => '/Users/<USER>/archive.zip', 'sourceDirectory' => '/Users/<USER>/Magento']
        );
        $this->assertEquals(
            'backup {"targetArchivePath":"/Users/<USER>/archive.zip","sourceDirectory":"/Users/<USER>/Magento"}',
            (string)$job
        );
    }
}
