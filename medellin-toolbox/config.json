{"css": {"inputPath": "../app/design/frontend/Inchoo/keh/src/css/", "inputPathSell": "../app/code/Inchoo/ReverseCheckout/view/base/web/css/", "outputPathSell": "../app/code/Inchoo/ReverseCheckout/view/frontend/web/css/", "inputPathParent": "../app/design/frontend/Inchoo/keh/src/css/", "inputPathAll": "../app/design/frontend/Inchoo/keh/src/css/**/*.{css,pcss}", "destPath": "../app/design/frontend/Inchoo/keh/web/css/", "destPathParent": "../app/design/frontend/Inchoo/keh/web/css/", "pluginConfig": {"postcssPresetEnv": {"autoprefixer": false, "stage": 0, "features": {"nesting-rules": true, "postcss-custom-properties": {"preserve": false}}}, "easyImport": {"extensions": [".css", ".pcss"], "prefix": "_", "path": ["../app/design/frontend/Inchoo/keh/src/css/**/[^_]*.{css,pcss}"]}, "cssNano": {"preset": "default"}}}, "browsers": ["last 3 versions", "not ie < 10", "safari >=5"], "projectUrl": "http://keh-m2-new.loc", "magentoLESStoPCSS": {"input": "../app/design/frontend/Inchoo/keh/web/css/source/_theme.less", "output": "../app/design/frontend/Inchoo/keh/src/css/vendor/"}, "mixinVR": {"vrSetup": [{"initial": {"baseFontSize": "14px", "baseLineHeight": "19.6px"}}, {"768px": {"baseFontSize": "15px", "baseLineHeight": "21px"}}, {"1024px": {"baseFontSize": "16px", "baseLineHeight": "22.4px"}}]}}