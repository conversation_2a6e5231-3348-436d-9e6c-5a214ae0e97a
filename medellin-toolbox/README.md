# medellin-toolbox
Set of tools and plugins to compile css for Medellin project
​
# Medellin toolbox on the default Magento 2
​
Sometimes we need to install Inchoo Medellin toolbox and Medellin Magento 2 theme to the default Magento 2 installation. 
Mostly for testing purposes or for further development of the Inchoo Medellin theme.
​
In order to set Inchoo Medellin toolbox on the default Magento installation please follow this steps:
​
- Open the composer.json in the html root file
​
    - Find **repositories** sections in the file:
    
    - Add the new composer url component:
        ```
         "repositories": [{
            "type": "composer",
            "url": "https://composer.inchoo.io"
          }]
​
    - Then find **require** section in the same composer.json file add :
        ```    
        "inchoodev/component-medellin-toolbox": "^1.1.6",
        "inchoodev/theme-frontend-medellin": "1.1.6"
​
​
- Next, open the `./hooli console`:
    - Enter the commands:
    
        `composer require inchoodev/component-medellin-toolbox:1.1.6`
    
        `composer require inchoodev/component-medellin-toolbox:1.1.6`
        
        `composer install
​
- After composer finish it, exit `./hooli console` and run the:
​
        `./hooli pull`
