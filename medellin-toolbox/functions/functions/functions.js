const config = require('../config');
module.exports = {
    pxtorem: (inputValue, baseFontSize = 16) => {
        let calcConversion = (parseFloat(inputValue, 10) / baseFontSize),
            finalresult = calcConversion + "rem";
        return finalresult
    },
    elevation: function (element) {
        let finalIndexNumber;
        if(!config.stacking.level1.includes(element)) {
            finalIndexNumber = console.log('There is no stacking context level element you just typed in.');
        }
        config.stacking.level1.forEach((el, i) => {
            if (el.includes(element)) {
                const zindexNumber = i + 1;
                finalIndexNumber = parseInt(zindexNumber);
            }
        });
        return finalIndexNumber
    }
}