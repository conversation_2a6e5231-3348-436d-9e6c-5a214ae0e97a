const postcss = require('../node_modules/postcss');
const colorConverter = require("../node_modules/color-convert");
const config = require("../config");
const colorSystem = config.colorSystem;

const postcssplugin  = postcss.plugin('color-system', opts => {

  // Work with options here

  const baseColors = Object.entries(colorSystem.targetColors),
          colorTones = colorSystem.tones;
      let hslColorsRaw = [];

  baseColors.forEach(([name, value], i) => {
      hslColorsRaw.push(colorConverter.hex.hsl(value));
  });

  let shadeColors = [],
      targetColorsProps = Object.keys(colorSystem.targetColors),
      targetColorsHsl = {},
      shadeColorsHsl = {};

  targetColorsProps.forEach((key, i) => targetColorsHsl[key] = hslColorsRaw[i]);

  for (const [key, value] of Object.entries(targetColorsHsl)) {
      let l = targetColorsHsl[key][0];
      shadeColorsHsl[key + '__base'] = targetColorsHsl[key];

      colorTones.forEach((tone, index) => {
          let h = targetColorsHsl[key][0],
              s = targetColorsHsl[key][1],
              l = tone;
          shadeColorsHsl[key + '__' + l] = [h, s, l];
      });
  }

  const shadeColorsHslFinal = Object.entries(shadeColorsHsl),
        rule = postcss.rule({selector: ":root"});

  shadeColorsHslFinal.forEach(([name, value], i) => {
    let [h, s, l] = value;

    rule.append({
        prop: '--' + name,
        value: 'hsl(' + h + ', ' + s + '%, ' + l + '%)'
    });

  });
  
  return (root, result) => {
    root.append(rule);
  };

});

module.exports = postcssplugin;