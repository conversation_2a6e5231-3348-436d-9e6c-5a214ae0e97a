/*
1. Replace projectUrls in config.json with example bellow.
2. To create critical CSS files run in medellin-toolbox folder "node critical/critical.js" after final CSS changes
and before deployment
3. Created files will be located in /web/css/ with "critical_layout_handle" as a filename. Critical CSS will be automaticaly
loaded after landing on page with assigned layout handle.
4. critical.css will be loaded only on pages which don't have their own generated critical CSS

Example for config.json:

"criticalCssUrls": {
    "cms_index_index": "http://www.medellin.loc",
    "catalog_category_view": "http://www.medellin.loc/women/tops-women/jackets-women.html",
    "catalog_product_view": "http://www.medellin.loc/mona-pullover-hoodlie.html"
}*/

const penthouse = require('penthouse'),
    fs = require('fs'),
    config = require('../config'),
    criticalCssUrls = config.criticalCssUrls,
    options = {
        css: config.css.destPath + 'styles.css',
        width: 1920,
        height: 900,
        forceExclude: [
            '[data-role=\'main-css-loader\'].loading-mask'
        ]
    };

function createDefaultCriticalCSS() {
    options.height = 170;
    const url = criticalCssUrls.cms_index_index;

    if (!url) {
        return Promise.resolve()
    }

    return penthouse({
        url,
        ...options
    }).then(criticalCss => {
        fs.writeFileSync(config.css.destPath + 'critical.css', criticalCss);
    })
}

function startCriticalTask() {
    if (!criticalCssUrls) {
        return Promise.resolve()
    }

    Object.entries(criticalCssUrls).forEach(entry => {
        const [layoutHandle, pageUrl] = entry;
        const url = pageUrl;

        return penthouse({
            url,
            ...options
        }).then(criticalCss => {
            fs.writeFileSync(config.css.destPath + 'critical_' + layoutHandle + '.css', criticalCss);
        })
    });
}

Promise.all([
    startCriticalTask(),
    createDefaultCriticalCSS(),
]).then(() => {
    console.log('Critical CSS is successfully generated!');
});



