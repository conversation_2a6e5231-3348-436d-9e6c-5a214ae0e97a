/* ===============
   PROJECT MEDELLIN
   =============== */

/* --- Simple yet effective CSS watcher & optimizer for project Medellin --- */

const gulp = require('gulp'),
    rename = require('gulp-rename'),
    postcss = require('gulp-postcss'),
    postcssPresetEnv = require('postcss-preset-env'),
    sourcemaps = require('gulp-sourcemaps'),
    easyImport = require('postcss-easy-import'),
    cssMixins = require('./mixins/vertical-rhythm'),
    cssFunctions = require('./functions/functions.js'),
    simpleVars = require('postcss-simple-vars'),
    baseVars = require('./mixins/variables'),
    path = require('path'),
    mixins = require('postcss-mixins')({
        mixins: cssMixins
    }),
    functions = require('postcss-functions')({
        functions: cssFunctions,
    }),
    atImport = require("postcss-import"),
    autoprefixer = require('autoprefixer'),
    slinkifyLESS = require('slinkify-less'),
    mqpacker = require('css-mqpacker'),
    cssnano = require('cssnano'),
    gulpStylelint = require('gulp-stylelint'),
    config = require('./config.json'),
    rtlcss = require('gulp-rtlcss'),
    lost = require('lost'),
    focusEnabled = require('postcss-focus'),
    colorSystem = require('./color-system'),
    fs = require('fs'),
    es = require('event-stream'),
    replace = require('gulp-replace'),
    livereload = require('gulp-livereload');

/* === BUILD TASKS === */

/* --- Default Welcome --- */

gulp.task('medelin:welcome', function () {
    return new Promise(function (resolve, reject) {
        console.log('\n' +
            '\n' +
            '                                                         /$$           /$$      /$$                 /$$           /$$ /$$          \n' +
            '                                                        | $$          | $$$    /$$$                | $$          | $$|__/          \n' +
            '  /$$$$$$   /$$$$$$   /$$$$$$  /$$  /$$$$$$   /$$$$$$$ /$$$$$$        | $$$$  /$$$$  /$$$$$$   /$$$$$$$  /$$$$$$ | $$ /$$ /$$$$$$$ \n' +
            ' /$$__  $$ /$$__  $$ /$$__  $$|__/ /$$__  $$ /$$_____/|_  $$_/        | $$ $$/$$ $$ /$$__  $$ /$$__  $$ /$$__  $$| $$| $$| $$__  $$\n' +
            '| $$  \\ $$| $$  \\__/| $$  \\ $$ /$$| $$$$$$$$| $$        | $$          | $$  $$$| $$| $$$$$$$$| $$  | $$| $$$$$$$$| $$| $$| $$  \\ $$\n' +
            '| $$  | $$| $$      | $$  | $$| $$| $$_____/| $$        | $$ /$$      | $$\\  $ | $$| $$_____/| $$  | $$| $$_____/| $$| $$| $$  | $$\n' +
            '| $$$$$$$/| $$      |  $$$$$$/| $$|  $$$$$$$|  $$$$$$$  |  $$$$/      | $$ \\/  | $$|  $$$$$$$|  $$$$$$$|  $$$$$$$| $$| $$| $$  | $$\n' +
            '| $$____/ |__/       \\______/ | $$ \\_______/ \\_______/   \\___/        |__/     |__/ \\_______/ \\_______/ \\_______/|__/|__/|__/  |__/\n' +
            '| $$                     /$$  | $$                                                                                                 \n' +
            '| $$                    |  $$$$$$/                                                                                                 \n' +
            '|__/                     \\______/                                                                                                  \n' +
            '\n' +
            'Welcome to the simple yet effective CSS watcher & optimizer for project Medelin. For development run: gulp dev, for production optimized code run: gulp prod.');
        resolve();
    })
});

/* --- PostCSS processors for dev environment --- */

var processorsDev = [
    atImport({path: [config.css.inputPathParent]}),
    simpleVars({
        silent: true,
        variables: baseVars
    }),
    mixins,
    functions,
    postcssPresetEnv(config.css.pluginConfig.postcssPresetEnv),
    lost(),
    focusEnabled,
    autoprefixer(config.browsers),
    mqpacker({
        sort: true
    })
];

/* --- PostCSS processors for production environment --- */

var processorsProd = [
    atImport({path: [config.css.inputPathParent]}),
    simpleVars({silent: true, variables: baseVars}),
    mixins,
    functions,
    postcssPresetEnv(config.css.pluginConfig.postcssPresetEnv),
    lost(),
    focusEnabled,
    autoprefixer(config.browsers),
    mqpacker({
        sort: true
    }),
    cssnano(config.css.pluginConfig.cssNano)
];

/* --- DEV COMPILE --- */

gulp.task('css:compile:dev', function (done) {
    return gulp
        .src(config.css.inputPath + 'styles.pcss')
        .pipe(sourcemaps.init({loadMaps: true}))
        .pipe(sourcemaps.identityMap())
        .pipe(postcss(processorsDev))
        .pipe(rename({extname: ".css"}))
        .pipe(sourcemaps.write())
        .pipe(gulp.dest(config.css.destPath))
        .pipe(livereload());
});

gulp.task('rtlcss', function(done) {
    return gulp
        .src(config.css.inputPathRTL)
        .pipe(rtlcss())
        .pipe(rename({
            suffix: '-rtl',
            extname: ".css"
        }))
        .pipe(gulp.dest(config.css.destPathRTL));
});

/* --- PRODUCTION COMPILE ( sourcemaps removed, NANO optimizer introduced ) --- */

gulp.task('css:compile:prod', function () {
    return gulp
        .src(config.css.inputPath + 'styles.pcss')
        .pipe(postcss(processorsProd))
        .pipe(rename({extname: ".css"}))
        .pipe(gulp.dest(config.css.destPath));
});

/* --- CSS Linter --- */

gulp.task('lint:css', function () {
    return gulp
        .src(config.css.inputPathAll)
        .pipe(gulpStylelint({
            reporters: [{formatter: 'string', console: true}]
        }));
});

/* --- Watchers --- */

gulp.task('css:watch:dev', function () {
    livereload.listen();
    return gulp
        .watch(config.css.inputPathAll, gulp.series('lint:css', 'css:compile:dev'));
});

gulp.task('css:watch:dev-rtl', function () {
    return gulp
        .watch(config.css.inputPathRTL, gulp.series('lint:css', 'css:compile:dev', 'rtlcss'));
});

gulp.task('css:watch:prod', function () {
    return gulp
        .watch(config.css.inputPathAll, gulp.series('css:compile:prod'));
});

gulp.task('css:watch:prod-rtl', function () {
    return gulp
        .watch(config.css.destPathAll, gulp.series('rtlcss'));
});

gulp.task('magento:less:pcss', function () {
    return gulp
        .src(config.magentoLESStoPCSS.input)
        .pipe(slinkifyLESS())
        .pipe(
            rename({
                extname: '.pcss'
            })
        )
        .pipe(gulp.dest(config.magentoLESStoPCSS.output));
});

/* --- Dynamic HSL color system --- */

gulp.task('frontend:color:system', function (cb) {
    fs.writeFile(config.colorSystem.inputPath + '_color-system.pcss', '', cb);
    let fileContent = fs.readFileSync(config.colorSystem.destPath + 'styles.pcss', 'utf8'),
        substring = '_color-system.pcss',
        anchor = '@import "abstracts/_variables.pcss";',
        replaceString;

    if (fileContent.includes(substring)) {
        replaceString = anchor;
    } else {
        replaceString = anchor + '\n@import \"abstracts/_color-system.pcss\";'
    }

    return es.concat(
        gulp
            .src(config.colorSystem.inputPath + '_color-system.pcss')
            .pipe(postcss([colorSystem]))
            .pipe(gulp.dest(config.colorSystem.inputPath)),
        gulp
            .src(config.colorSystem.destPath + 'styles.pcss')
            .pipe(replace(anchor, replaceString))
            .pipe (gulp.dest (config.colorSystem.destPath))
    );
});


/* --- Development/Production chained sets --- */

gulp.task("dev", gulp.series('lint:css', 'css:compile:dev', gulp.parallel('css:watch:dev')));
gulp.task("dev-rtl", gulp.series('lint:css', 'css:compile:dev', 'rtlcss', gulp.parallel('css:watch:dev', 'css:watch:dev-rtl')));

gulp.task("prod", gulp.series('lint:css', 'css:compile:prod'));
gulp.task("prod-rtl", gulp.series('lint:css', 'css:compile:prod', 'rtlcss'));

/* --- less to pcss variable converter --- */

gulp.task("lesstopcss", gulp.series('magento:less:pcss'));

/* --- Default/Welcome Message --- */

gulp.task("default", gulp.series('medelin:welcome'));
