{"css": {"inputPath": "../app/design/frontend/Inchoodev/medellin/src/css/", "inputPathParent": "../app/design/frontend/Inchoodev/medellin/src/css/", "inputPathAll": "../app/design/frontend/Inchoodev/medellin/src/css/**/*.{css,pcss}", "destPath": "../app/design/frontend/Inchoodev/medellin/web/css/", "destPathParent": "../app/design/frontend/Inchoodev/medellin/web/css/", "inputPathRTL": "../app/design/frontend/Inchoodev/medellin/web/css/*.{css,pcss}", "destPathRTL": "../app/design/frontend/Inchoodev/medellin-rtl/web/css/", "pluginConfig": {"postcssPresetEnv": {"autoprefixer": false, "stage": 0, "features": {"nesting-rules": true, "postcss-custom-properties": {"preserve": false}}}, "easyImport": {"extensions": [".css", ".pcss"], "prefix": "_", "path": ["../app/design/frontend/Inchoodev/medelin/src/css/**/[^_]*.{css,pcss}"]}, "cssNano": {"preset": "default"}}}, "browsers": ["last 3 versions", "not ie < 10", "safari >=5"], "projectUrl": "http://www.medelin.loc", "projectUrls": ["http://www.medellin.loc/category-2/category-2-1.html"], "magentoLESStoPCSS": {"input": "../app/design/frontend/Inchoodev/medellin/web/css/source/_theme.less", "output": "../app/design/frontend/Inchoodev/medellin/src/css/vendor/"}, "mixinVR": {"vrSetup": [{"initial": {"baseFontSize": "14px", "baseLineHeight": "21px"}}, {"768px": {"baseFontSize": "15px", "baseLineHeight": "22px"}}, {"1024px": {"baseFontSize": "16px", "baseLineHeight": "24px"}}]}, "stacking": {"level1": ["header-menu", "minicart", "main-nav"]}, "colorSystem": {"targetColors": {"green-color": "#789d34", "red-color": "#ac3d39", "blue-color": "#236367"}, "tones": [10, 20, 30, 40, 50, 60, 70, 80], "inputPath": "../app/design/frontend/Inchoodev/medellin/src/css/abstracts/", "destPath": "../app/design/frontend/Inchoodev/medellin/src/css/"}}