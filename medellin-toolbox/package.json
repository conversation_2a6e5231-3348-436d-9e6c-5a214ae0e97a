{"name": "medellin", "version": "1.0.4", "description": "Medellin frontend toolbox", "main": "gulpconfig.js", "scripts": {"test": "test", "critical": "node critical/critical.js"}, "repository": {"type": "git", "url": "git+https://github.com/Inchoo/Medelin.git"}, "author": "Fico <<EMAIL>>", "license": "ISC", "bugs": {"url": "https://github.com/Inchoo/Medelin/issues"}, "homepage": "https://github.com/Inchoo/Medelin#readme", "devDependencies": {"autoprefixer": "^9.4.10", "colors-convert": "^1.3.0", "css-mqpacker": "^7.0.0", "cssnano": "^4.1.10", "event-stream": "^4.0.1", "gulp": "^4.0.0", "gulp-livereload": "^4.0.2", "gulp-postcss": "^8.0.0", "gulp-rename": "^1.4.0", "gulp-replace": "^1.1.3", "gulp-rtlcss": "^1.4.0", "gulp-sourcemaps": "^2.6.5", "gulp-stylelint": "^8.0.0", "lost": "^8.3.1", "postcss-easy-import": "^3.0.0", "postcss-functions": "^3.0.0", "postcss-import": "^12.0.1", "postcss-mixins": "^6.2.1", "postcss-preset-env": "^6.6.0", "postcss-simple-vars": "^5.0.2", "slinkify-less": "https://github.com/codeAdrian/slinkify-less/archive/0.3.1.tar.gz", "stylelint": "^9.10.1"}, "dependencies": {"css-diff": "^0.4.1", "diff": "^5.0.0", "gulp-util": "^3.0.8", "magepack": "^2.4.0", "penthouse": "^2.3.1", "postcss-focus": "^4.0.0"}}